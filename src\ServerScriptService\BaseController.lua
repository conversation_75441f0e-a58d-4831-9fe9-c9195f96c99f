-- BaseController.lua
-- ModuleScript para controlar o tamanho visual das bases

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local BaseController = {}

-- Função para calcular escala baseada no BaseSize
local function calculateScale(baseSize)
    local minSize = GameConfig.BASE_CONFIG.BASE_SIZE_MIN
    local maxSize = GameConfig.BASE_CONFIG.BASE_SIZE_MAX
    local normalizedSize = math.max(0, math.min(1, (baseSize - minSize) / (maxSize - minSize)))
    return normalizedSize
end

-- Função para interpolar entre dois valores
local function lerp(a, b, t)
    return a + (b - a) * t
end

-- Função principal para atualizar o tamanho da base
function BaseController.UpdateBase(baseModel)
    if not baseModel or not baseModel.Parent then
        return false
    end
    
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    if not baseSizeValue then
        warn("BaseSize não encontrado em " .. baseModel.Name)
        return false
    end
    
    local baseSize = baseSizeValue.Value
    local scale = calculateScale(baseSize)
    
    -- Atualiza BasePlatform (corrigido)
    local basePlatform = baseModel:FindFirstChild("BasePlatform")
    if basePlatform then
        local minSize = GameConfig.BASE_CONFIG.PLATFORM_SIZE_MIN
        local maxSize = GameConfig.BASE_CONFIG.PLATFORM_SIZE_MAX
        local newSize = lerp(minSize, maxSize, scale)
        basePlatform.Size = Vector3.new(newSize, 2, newSize)  -- Mantém altura fixa
        -- Reposiciona para manter no chão
        basePlatform.Position = Vector3.new(basePlatform.Position.X, 1, basePlatform.Position.Z)
    end
    
    -- Atualiza Barrier (corrigido)
    local barrier = baseModel:FindFirstChild("Barrier")
    if barrier then
        local minHeight = GameConfig.BASE_CONFIG.BARRIER_HEIGHT_MIN
        local maxHeight = GameConfig.BASE_CONFIG.BARRIER_HEIGHT_MAX
        local newHeight = lerp(minHeight, maxHeight, scale)
        local newRadius = lerp(GameConfig.BASE_CONFIG.PLATFORM_SIZE_MIN, GameConfig.BASE_CONFIG.PLATFORM_SIZE_MAX, scale)
        
        barrier.Size = Vector3.new(newRadius, newRadius, newRadius)  -- Esfera perfeita
        barrier.Position = Vector3.new(barrier.Position.X, newRadius / 2, barrier.Position.Z)  -- Toca o chão
    end
    
    -- Verifica condição de derrota
    BaseController.CheckDefeatCondition(baseModel)
    
    -- Verifica construções que ficaram fora da barreira
    local BuildingManager = require(script.Parent.BuildingManager)
    if BuildingManager and BuildingManager.CheckBuildingsInBarrier then
        BuildingManager.CheckBuildingsInBarrier(baseModel)
    end
    
    return true
end

-- Função para verificar condição de derrota
function BaseController.CheckDefeatCondition(baseModel)
    if not baseModel or not baseModel.Parent then
        return
    end
    
    local barrier = baseModel:FindFirstChild("Barrier")
    local coreTower = baseModel:FindFirstChild("CoreTower")
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    
    if not barrier or not coreTower or not baseSizeValue then
        return
    end
    
    -- Verifica se a base atingiu o tamanho mínimo ou se a barreira tocou a torre
    local barrierRadius = barrier.Size.X / 2
    local towerPosition = coreTower.Position
    local barrierPosition = barrier.Position
    local distance = (towerPosition - barrierPosition).Magnitude
    
    if distance <= barrierRadius or baseSizeValue.Value <= GameConfig.BASE_CONFIG.BASE_SIZE_MIN then
        BaseController.DestroyBase(baseModel)
    end
end

-- Função para destruir/resetar uma base
function BaseController.DestroyBase(baseModel)
    if not baseModel or not baseModel.Parent then
        return
    end
    
    print("Base " .. baseModel.Name .. " foi destruída!")
    
    -- Remove jogadores da base
    local owner = baseModel:FindFirstChild("Owner")
    local partner = baseModel:FindFirstChild("Partner")
    local spawnLocation = baseModel:FindFirstChild("SpawnLocation")
    
    if owner and owner.Value then
        owner.Value.RespawnLocation = nil
        owner.Value = nil
    end
    
    if partner and partner.Value then
        partner.Value.RespawnLocation = nil
        partner.Value = nil
    end
    
    -- Reseta valores
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    local buildingMaterialsValue = baseModel:FindFirstChild("BuildingMaterials")
    
    if baseSizeValue then
        baseSizeValue.Value = GameConfig.BASE_CONFIG.BASE_SIZE_DEFAULT
    end
    
    if buildingMaterialsValue then
        buildingMaterialsValue.Value = 0
    end
    
    -- Desativa spawn
    if spawnLocation then
        spawnLocation.Enabled = false
    end
    
    -- Reseta cores para padrão
    local basePlatform = baseModel:FindFirstChild("BasePlatform")
    local coreTower = baseModel:FindFirstChild("CoreTower")
    local barrier = baseModel:FindFirstChild("Barrier")
    
    if basePlatform then
        basePlatform.BrickColor = BrickColor.new("Bright green")
    end
    
    if coreTower then
        coreTower.BrickColor = BrickColor.new("Dark stone grey")
        local flag = coreTower:FindFirstChild("Flag")
        if flag then
            flag.BrickColor = BrickColor.new("Really red")
        end
    end
    
    if barrier then
        barrier.BrickColor = BrickColor.new("Cyan")
        barrier.Transparency = 0.7
    end
    
    -- Reativa o ClaimPad
    local claimPad = baseModel:FindFirstChild("ClaimPad")
    if claimPad then
        claimPad.BrickColor = BrickColor.new("Bright yellow")
    end
    
    -- Atualiza tamanho visual
    BaseController.UpdateBase(baseModel)
    
    -- Efeito visual de destruição
    BaseController.CreateDestructionEffect(baseModel)
end

-- Função para criar efeito visual de destruição
function BaseController.CreateDestructionEffect(baseModel)
    local coreTower = baseModel:FindFirstChild("CoreTower")
    if not coreTower then return end
    
    -- Cria explosão
    local explosion = Instance.new("Explosion")
    explosion.Position = coreTower.Position
    explosion.BlastRadius = 20
    explosion.BlastPressure = 0
    explosion.Parent = workspace
    
    print("Efeito de destruição criado para " .. baseModel.Name)
end

-- Função para verificar se um jogador está dentro da barreira
function BaseController.IsPlayerInBarrier(player, baseModel)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local barrier = baseModel:FindFirstChild("Barrier")
    if not barrier then return false end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local barrierPosition = barrier.Position
    local barrierRadius = barrier.Size.X / 2
    
    local distance = (playerPosition - barrierPosition).Magnitude
    return distance <= barrierRadius
end

-- Função para verificar se um jogador é da equipe da base
function BaseController.IsPlayerTeammate(player, baseModel)
    local owner = baseModel:FindFirstChild("Owner")
    local partner = baseModel:FindFirstChild("Partner")
    
    return (owner and owner.Value == player) or (partner and partner.Value == player)
end

return BaseController