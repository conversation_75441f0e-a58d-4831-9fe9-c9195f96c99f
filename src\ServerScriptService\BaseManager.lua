-- BaseManager.lua
-- Gerenciador principal das bases reivindicáveis

local Players = game:GetService("Players")
local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))
local BaseController = require(script.Parent.BaseController)

local BaseManager = {}

-- Variáveis globais
local activeBases = {}
local playerBases = {} -- Mapeia jogador para sua base

-- Cria uma base individual com cor única
local function createBase(position, index)
    local baseTemplate = ServerStorage:FindFirstChild("BaseTemplate")
    if not baseTemplate then
        warn("BaseTemplate não encontrado em ServerStorage!")
        return nil
    end

    local base = baseTemplate:Clone()
    base.Name = "Base_" .. index
    base.Parent = workspace

    -- Posiciona a base corretamente
    if base.PrimaryPart then
        base:SetPrimaryPartCFrame(CFrame.new(position))
    else
        -- Move manualmente todos os componentes
        local basePlatform = base:FindFirstChild("BasePlatform")
        if basePlatform then
            local offset = position - basePlatform.Position
            for _, part in ipairs(base:GetDescendants()) do
                if part:IsA("BasePart") then
                    part.Position = part.Position + offset
                end
            end
        end
    end

    -- Aplica cor única para cada base (cada base tem sua própria cor)
    local colorIndex = ((index - 1) % #GameConfig.TEAM_COLORS) + 1
    local teamColor = GameConfig.TEAM_COLORS[colorIndex]
    local baseColor = BrickColor.new(teamColor)

    -- Aplica cor aos componentes da base
    local basePlatform = base:FindFirstChild("BasePlatform")
    local coreTower = base:FindFirstChild("CoreTower")
    local barrier = base:FindFirstChild("Barrier")
    local claimPad = base:FindFirstChild("ClaimPad")
    
    -- Encontra a bandeira dentro da CoreTower
    local flag = nil
    if coreTower then
        flag = coreTower:FindFirstChild("Flag")
    end

    -- Aplica cores padrão para base não reivindicada
    if basePlatform then 
        basePlatform.BrickColor = BrickColor.new("Dark stone grey")
    end
    if coreTower then 
        coreTower.BrickColor = BrickColor.new("Dark stone grey")
    end
    if barrier then 
        barrier.BrickColor = BrickColor.new("Cyan")
        barrier.Transparency = 0.7
    end
    if claimPad then 
        claimPad.BrickColor = BrickColor.new("Bright yellow")
    end
    
    -- A bandeira já tem a cor da equipe para identificação
    if flag then
        flag.BrickColor = baseColor

        -- Orienta a bandeira para o centro do mapa (0, 0, 0)
        local flagPosition = flag.Position
        local centerDirection = (Vector3.new(0, flagPosition.Y, 0) - flagPosition).Unit
        local lookDirection = Vector3.new(centerDirection.X, 0, centerDirection.Z).Unit

        -- Calcula a rotação necessária
        local angle = math.atan2(lookDirection.X, lookDirection.Z)
        flag.CFrame = CFrame.new(flag.Position) * CFrame.Angles(0, angle, 0)
    end

    -- Armazena a cor da equipe para uso posterior
    local teamColorValue = Instance.new("StringValue")
    teamColorValue.Name = "TeamColor"
    teamColorValue.Value = teamColor
    teamColorValue.Parent = base

    print("Base " .. index .. " criada na posição " .. tostring(position) .. " com cor " .. teamColor)

    return base
end

-- Atribui a cor específica da base ao jogador quando reivindicada
local function assignColorToBase(base, player)
    local teamColorValue = base:FindFirstChild("TeamColor")
    if not teamColorValue then
        warn("TeamColor não encontrado na base!")
        return false
    end
    
    local teamColor = teamColorValue.Value
    local color = BrickColor.new(teamColor)
    
    -- Muda a cor dos componentes da base para a cor da equipe
    local basePlatform = base:FindFirstChild("BasePlatform")
    local coreTower = base:FindFirstChild("CoreTower")
    local barrier = base:FindFirstChild("Barrier")
    local claimPad = base:FindFirstChild("ClaimPad")
    
    if basePlatform then basePlatform.BrickColor = color end
    if coreTower then 
        coreTower.BrickColor = color 
        
        -- A bandeira mantém a cor da equipe (já estava correta)
        local flag = coreTower:FindFirstChild("Flag")
        if flag then
            flag.BrickColor = color
        end
    end
    if barrier then 
        barrier.BrickColor = color
        barrier.Transparency = 0.5  -- Menos transparente quando reivindicada
    end
    if claimPad then 
        claimPad.BrickColor = color
    end
    
    print("Base reivindicada por " .. player.Name .. " com cor " .. teamColor)
    
    return true
end

-- Configura o evento de reivindicação da base
local function setupClaimPad(base)
    local claimPad = base:FindFirstChild("ClaimPad")
    local owner = base:FindFirstChild("Owner")
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    if not claimPad or not owner or not spawnLocation then
        warn("Componentes da base não encontrados!")
        return
    end
    
    local connection
    connection = claimPad.Touched:Connect(function(hit)
        local humanoid = hit.Parent:FindFirstChild("Humanoid")
        if not humanoid then return end
        
        local player = Players:GetPlayerFromCharacter(hit.Parent)
        if not player then return end
        
        -- Verifica se a base já tem dono
        if owner.Value ~= nil then return end
        
        -- Verifica se o jogador já tem uma base
        if playerBases[player] then return end
        
        -- Reivindica a base
        owner.Value = player
        playerBases[player] = base
        
        -- Atribui cor à base e ao jogador
        if assignColorToBase(base, player) then
            -- Ativa o spawn da base
            spawnLocation.Enabled = true
            player.RespawnLocation = spawnLocation
            
            print(player.Name .. " reivindicou a base " .. base.Name)
            
            -- Desconecta o evento de toque
            connection:Disconnect()
            
            -- Atualiza UI do jogador
            local updateBaseInfo = ReplicatedStorage.RemoteEvents:FindFirstChild("UpdateBaseInfo")
            if updateBaseInfo then
                updateBaseInfo:FireClient(player, base.Name, base.BaseSize.Value, base.BuildingMaterials.Value)
            end
        else
            -- Se não conseguiu atribuir cor, reverte a reivindicação
            owner.Value = nil
            playerBases[player] = nil
        end
    end)
end

-- Limpa a base quando o jogador sai
local function cleanupPlayerBase(player)
    local base = playerBases[player]
    if not base then return end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    -- Se o jogador era o dono
    if owner and owner.Value == player then
        -- Se há um parceiro, promove ele a dono
        if partner and partner.Value then
            owner.Value = partner.Value
            partner.Value = nil
            playerBases[partner.Value] = base
        else
            -- Libera a base completamente
            owner.Value = nil
            spawnLocation.Enabled = false
            
            -- Reseta a base para cores padrão
            local basePlatform = base:FindFirstChild("BasePlatform")
            local coreTower = base:FindFirstChild("CoreTower")
            local barrier = base:FindFirstChild("Barrier")
            local claimPad = base:FindFirstChild("ClaimPad")
            
            if basePlatform then basePlatform.BrickColor = BrickColor.new("Dark stone grey") end
            if coreTower then coreTower.BrickColor = BrickColor.new("Dark stone grey") end
            if barrier then 
                barrier.BrickColor = BrickColor.new("Cyan")
                barrier.Transparency = 0.7
            end
            if claimPad then claimPad.BrickColor = BrickColor.new("Bright yellow") end
            
            -- Reativa o ClaimPad
            setupClaimPad(base)
        end
    elseif partner and partner.Value == player then
        -- Se o jogador era apenas parceiro
        partner.Value = nil
    end
    
    playerBases[player] = nil
end

-- Inicializa o sistema de bases
local function initializeBases()
    for i, position in ipairs(GameConfig.BASE_POSITIONS) do
        local base = createBase(position, i)
        if base then
            table.insert(activeBases, base)
            setupClaimPad(base)
        end
    end
    
    print("Sistema de bases inicializado com " .. #activeBases .. " bases")
end

-- Sistema de efeitos da barreira
local function applyBarrierEffects()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("Humanoid") then
            local humanoid = player.Character.Humanoid
            
            -- Verifica todas as bases
            for _, base in ipairs(activeBases) do
                if base and base.Parent then
                    local isInBarrier = BaseController.IsPlayerInBarrier(player, base)
                    
                    if isInBarrier then
                        local isTeammate = BaseController.IsPlayerTeammate(player, base)
                        
                        if isTeammate then
                            -- Cura jogador da própria equipe
                            if humanoid.Health < humanoid.MaxHealth then
                                humanoid.Health = math.min(humanoid.MaxHealth, 
                                    humanoid.Health + GameConfig.BARRIER_CONFIG.HEAL_PER_SECOND)
                            end
                        else
                            -- Dano a jogador inimigo
                            local owner = base:FindFirstChild("Owner")
                            if owner and owner.Value then -- Só causa dano se a base tem dono
                                humanoid.Health = math.max(0, 
                                    humanoid.Health - GameConfig.BARRIER_CONFIG.DAMAGE_PER_SECOND)
                            end
                        end
                        break -- Sai do loop se encontrou uma barreira
                    end
                end
            end
        end
    end
end

-- Conecta eventos de jogadores
Players.PlayerRemoving:Connect(cleanupPlayerBase)

-- Conecta ao evento de morte dos jogadores
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function(character)
        local humanoid = character:WaitForChild("Humanoid")
        
        humanoid.Died:Connect(function()
            -- Encontra a base do jogador
            local base = playerBases[player]
            if base then
                local baseSizeValue = base:FindFirstChild("BaseSize")
                if baseSizeValue then
                    baseSizeValue.Value = math.max(GameConfig.BASE_CONFIG.BASE_SIZE_MIN, 
                        baseSizeValue.Value - GameConfig.COMBAT_CONFIG.DEATH_BASE_REDUCTION)
                    
                    -- Atualiza visualmente a base
                    BaseController.UpdateBase(base)
                    
                    print(player.Name .. " morreu! Base " .. base.Name .. " perdeu tamanho.")
                end
            end
            
            -- Verifica se estava carregando recurso
            if character:FindFirstChild("CarregandoRecurso") then
                print(player.Name .. " perdeu o recurso que estava carregando!")
            end
            
            -- Determina tempo de respawn
            local respawnTime = GameConfig.RESPAWN_CONFIG.SOLO_TIME
            if base then
                local partner = base:FindFirstChild("Partner")
                local owner = base:FindFirstChild("Owner")
                
                -- Se tem parceiro, tempo de respawn é maior
                if (owner and owner.Value and owner.Value ~= player) or 
                   (partner and partner.Value and partner.Value ~= player) then
                    respawnTime = GameConfig.RESPAWN_CONFIG.TEAM_TIME
                end
            end
            
            -- Agenda o respawn
            spawn(function()
                wait(respawnTime)
                if player.Parent then
                    player:LoadCharacter()
                end
            end)
        end)
    end)
end)

-- Loop de verificação dos efeitos da barreira
spawn(function()
    while true do
        wait(GameConfig.BARRIER_CONFIG.CHECK_INTERVAL)
        applyBarrierEffects()
    end
end)

-- Função para atualizar tamanho da base (para outros scripts)
function BaseManager.updateBaseSize(base)
    return BaseController.UpdateBase(base)
end

-- Função para obter base de um jogador (para outros scripts)
function BaseManager.getPlayerBase(player)
    return playerBases[player]
end

-- Inicializa quando o script carrega
initializeBases()

return BaseManager