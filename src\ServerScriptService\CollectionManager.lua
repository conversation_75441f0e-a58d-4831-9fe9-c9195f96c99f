-- CollectionManager.lua
-- Gerencia a coleta de recursos no servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollecting = remoteEvents:WaitForChild("StartCollecting")
local stopCollecting = remoteEvents:WaitForChild("StopCollecting")
local collectComplete = remoteEvents:WaitForChild("CollectComplete")

local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local CollectionManager = {}

-- Configurações
local COLLECTION_SLOWDOWN = GameConfig.RESOURCE_CONFIG.COLLECTION_SLOWDOWN
local NORMAL_WALKSPEED = 32  -- Dobrado de 16 para 32

-- Controle de coleta
local collectingResources = {} -- [resource] = {collector, startTime, originalSize}

-- Função para aplicar lentidão ao jogador
local function applySlowdown(player)
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        humanoid.WalkSpeed = NORMAL_WALKSPEED * COLLECTION_SLOWDOWN
    end
end

-- Função para remover lentidão do jogador
local function removeSlowdown(player)
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        humanoid.WalkSpeed = NORMAL_WALKSPEED
    end
end

-- Função para definir estado de carregamento de recurso
local function setCarryingResource(player, carrying, resourceValue)
    if not player.Character then return end
    
    local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
    if not carryingValue then
        carryingValue = Instance.new("BoolValue")
        carryingValue.Name = "CarregandoRecurso"
        carryingValue.Parent = player.Character
    end
    
    carryingValue.Value = carrying
    
    if carrying then
        -- Adiciona valor do recurso
        local resourceValueObj = player.Character:FindFirstChild("ResourceValue")
        if not resourceValueObj then
            resourceValueObj = Instance.new("NumberValue")
            resourceValueObj.Name = "ResourceValue"
            resourceValueObj.Parent = player.Character
        end
        resourceValueObj.Value = resourceValue or 0
        
        -- Aplica lentidão
        applySlowdown(player)
        
        print(player.Name .. " está carregando recurso no valor de " .. resourceValue)
    else
        -- Remove valor do recurso
        local resourceValueObj = player.Character:FindFirstChild("ResourceValue")
        if resourceValueObj then
            resourceValueObj:Destroy()
        end
        
        -- Remove lentidão
        removeSlowdown(player)
    end
end

-- Manipula início da coleta
startCollecting.OnServerEvent:Connect(function(player, resource)
    if not resource or not resource.Parent then return end
    
    -- Verifica se o jogador já está carregando um recurso
    if player.Character and player.Character:FindFirstChild("CarregandoRecurso") then
        local carrying = player.Character.CarregandoRecurso.Value
        if carrying then
            return -- Já está carregando um recurso
        end
    end
    
    -- Verifica se o recurso tem os componentes necessários
    local originalSize = resource:FindFirstChild("OriginalSize")
    if not originalSize then
        return
    end
    
    -- Verifica distância
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        local distance = (player.Character.HumanoidRootPart.Position - resource.Position).Magnitude
        if distance > 100 then -- Máximo 100 studs
            return
        end
    end
    
    -- Inicia a coleta
    if not collectingResources[resource] then
        collectingResources[resource] = {
            collector = player,
            startTime = tick(),
            originalSize = originalSize.Value
        }
        
        print(player.Name .. " iniciou coleta de " .. resource.Name)
    end
end)

-- Manipula parada da coleta
stopCollecting.OnServerEvent:Connect(function(player, resource)
    if not resource then return end
    
    local collectData = collectingResources[resource]
    if collectData and collectData.collector == player then
        collectingResources[resource] = nil
        print(player.Name .. " parou coleta de " .. resource.Name)
    end
end)

-- Atualiza o tamanho dos recursos sendo coletados
local function updateResourceSizes()
    for resource, data in pairs(collectingResources) do
        if resource and resource.Parent then
            -- Encolhe gradualmente
            local originalSize = data.originalSize
            local currentSize = resource.Size
            local shrinkRate = 0.02 -- Taxa de encolhimento por frame
            
            -- Calcula novo tamanho
            local newSize = Vector3.new(
                math.max(originalSize.X * 0.1, currentSize.X - shrinkRate),
                math.max(originalSize.Y * 0.1, currentSize.Y - shrinkRate),
                math.max(originalSize.Z * 0.1, currentSize.Z - shrinkRate)
            )
            
            resource.Size = newSize
            
            -- Verifica se atingiu o tamanho mínimo (coleta completa)
            if newSize.X <= originalSize.X * 0.1 then
                local resourceValue = resource:FindFirstChild("ResourceValue")
                local value = resourceValue and resourceValue.Value or 10
                
                -- Define o estado de carregamento
                setCarryingResource(data.collector, true, value)
                
                -- Remove da lista de coleta
                collectingResources[resource] = nil
                
                -- Remove o recurso
                resource:Destroy()
                
                print(data.collector.Name .. " coletou recurso no valor de " .. value)
            end
        else
            -- Remove recurso inválido
            collectingResources[resource] = nil
        end
    end
    
    -- Faz recursos não sendo coletados crescerem de volta
    for _, resource in ipairs(workspace:GetChildren()) do
        if resource:FindFirstChild("OriginalSize") and not collectingResources[resource] then
            local originalSizeValue = resource.OriginalSize.Value
            local currentSize = resource.Size
            
            if currentSize.Magnitude < originalSizeValue.Magnitude then
                local growRate = 0.05 -- Taxa de crescimento
                local newSize = Vector3.new(
                    math.min(originalSizeValue.X, currentSize.X + growRate),
                    math.min(originalSizeValue.Y, currentSize.Y + growRate),
                    math.min(originalSizeValue.Z, currentSize.Z + growRate)
                )
                resource.Size = newSize
            end
        end
    end
end

-- Loop de atualização
RunService.Heartbeat:Connect(updateResourceSizes)

-- Limpa estado quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    -- Para qualquer coleta em andamento
    for resource, data in pairs(collectingResources) do
        if data.collector == player then
            collectingResources[resource] = nil
        end
    end
end)

-- Restaura velocidade quando jogador spawna
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function(character)
        wait(1) -- Aguarda o character carregar completamente
        removeSlowdown(player)
    end)
end)

print("CollectionManager inicializado com sucesso!")

return CollectionManager