-- ForceCreateTemplate.lua
-- Script para forçar a criação do template da base

local ServerStorage = game:GetService("ServerStorage")

-- Aguarda um pouco para garantir que outros scripts carregaram
wait(2)

-- Executa o script de criação do template
local success, error = pcall(function()
    require(ServerStorage.CreateBaseTemplate)
end)

if success then
    print("Template da base criado com sucesso!")
else
    warn("Erro ao criar template da base:", error)
end