-- ForceSpawnNearBases.lua
-- Sistema robusto para forçar spawn perto de bases não reivindicadas

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Função para encontrar bases não reivindicadas
local function findUnclaimedBases()
    local unclaimedBases = {}
    
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj.Name:match("Base_") and obj:FindFirstChild("Owner") then
            local owner = obj.Owner.Value
            if not owner then
                table.insert(unclaimedBases, obj)
                print("🏠 Base não reivindicada encontrada: " .. obj.Name)
            end
        end
    end
    
    return unclaimedBases
end

-- Função para criar spawn forçado
local function createForceSpawn(base, player)
    -- Remove spawns antigos do jogador
    for _, spawn in ipairs(workspace:GetChildren()) do
        if spawn.Name == "ForceSpawn_" .. player.Name then
            spawn:Destroy()
        end
    end
    
    local spawnLocation = Instance.new("SpawnLocation")
    spawnLocation.Name = "ForceSpawn_" .. player.Name
    spawnLocation.Size = Vector3.new(10, 1, 10)
    spawnLocation.BrickColor = BrickColor.new("Lime green")
    spawnLocation.Material = Enum.Material.Neon
    spawnLocation.Anchored = true
    spawnLocation.CanCollide = true
    spawnLocation.Parent = workspace
    
    -- Encontra posição da base
    local basePosition = Vector3.new(0, 0, 0)
    local claimPad = base:FindFirstChild("ClaimPad")
    
    if claimPad then
        basePosition = claimPad.Position
    else
        -- Calcula centro da base
        local totalPos = Vector3.new(0, 0, 0)
        local count = 0
        for _, child in ipairs(base:GetChildren()) do
            if child:IsA("BasePart") then
                totalPos = totalPos + child.Position
                count = count + 1
            end
        end
        if count > 0 then
            basePosition = totalPos / count
        end
    end
    
    -- Posiciona spawn na frente da base
    spawnLocation.Position = basePosition + Vector3.new(0, 5, 30)
    
    -- Adiciona texto
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = spawnLocation
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "🎯 " .. player.Name .. " SPAWN\n📍 " .. base.Name
    textLabel.TextColor3 = Color3.new(0, 0, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    -- Adiciona luz
    local pointLight = Instance.new("PointLight")
    pointLight.Color = Color3.new(0, 1, 0)
    pointLight.Brightness = 3
    pointLight.Range = 30
    pointLight.Parent = spawnLocation
    
    -- Define como spawn do jogador
    player.RespawnLocation = spawnLocation
    
    print("✅ Spawn forçado criado para " .. player.Name .. " perto de " .. base.Name)
    
    return spawnLocation
end

-- Função para configurar spawn de um jogador
local function setupPlayerSpawn(player)
    local unclaimedBases = findUnclaimedBases()
    
    if #unclaimedBases > 0 then
        local selectedBase = unclaimedBases[math.random(1, #unclaimedBases)]
        local spawn = createForceSpawn(selectedBase, player)
        
        -- Remove spawn após 2 minutos
        spawn(function()
            wait(120)
            if spawn and spawn.Parent then
                spawn:Destroy()
                print("🗑️ Spawn temporário de " .. player.Name .. " removido")
            end
        end)
    else
        print("⚠️ Nenhuma base não reivindicada encontrada para " .. player.Name)
    end
end

-- Remove spawn central se existir
local centralSpawn = workspace:FindFirstChild("CentralSpawn")
if centralSpawn then
    centralSpawn:Destroy()
    print("🗑️ Spawn central removido")
end

-- Conecta aos jogadores
Players.PlayerAdded:Connect(function(player)
    print("👤 Jogador " .. player.Name .. " entrou - configurando spawn...")
    
    -- Aguarda um pouco para garantir que as bases foram criadas
    wait(2)
    setupPlayerSpawn(player)
    
    -- Reconfigura spawn quando o jogador respawna
    player.CharacterAdded:Connect(function()
        wait(1)
        
        -- Verifica se o jogador já tem uma base
        local hasBase = false
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") and base:FindFirstChild("Owner") then
                local owner = base.Owner.Value
                if owner == player then
                    hasBase = true
                    break
                end
            end
        end
        
        -- Se não tem base, reconfigura spawn
        if not hasBase then
            setupPlayerSpawn(player)
        end
    end)
end)

-- Configura spawn para jogadores já conectados
for _, player in ipairs(Players:GetPlayers()) do
    setupPlayerSpawn(player)
end

print("🎯 ForceSpawnNearBases inicializado - jogadores spawnarão perto de bases!")

return true