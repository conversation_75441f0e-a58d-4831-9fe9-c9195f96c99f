-- PlayerSpeedManager.lua
-- Gerencia a velocidade padrão dos jogadores

local Players = game:GetService("Players")

-- Velocidade padrão dobrada
local DEFAULT_WALKSPEED = 32
local DEFAULT_JUMPPOWER = 50

-- Função para definir velocidade padrão
local function setDefaultSpeed(player)
    if not player.Character then return end
    
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        humanoid.WalkSpeed = DEFAULT_WALKSPEED
        humanoid.JumpPower = DEFAULT_JUMPPOWER
        print("Velocidade padrão definida para " .. player.Name .. ": " .. DEFAULT_WALKSPEED)
    end
end

-- Conecta aos jogadores existentes
for _, player in ipairs(Players:GetPlayers()) do
    if player.Character then
        setDefaultSpeed(player)
    end
    
    player.CharacterAdded:Connect(function()
        wait(1) -- Aguarda o character carregar
        setDefaultSpeed(player)
    end)
end

-- Conecta aos novos jogadores
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function()
        wait(1) -- Aguarda o character carregar
        setDefaultSpeed(player)
    end)
end)

print("PlayerSpeedManager inicializado - velocidade padrão: " .. DEFAULT_WALKSPEED)

return true
