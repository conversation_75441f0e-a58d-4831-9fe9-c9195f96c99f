-- ResourceManager.lua
-- Gerencia recursos coletáveis no mapa

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Carrega configurações
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local ResourceManager = {}

-- Tipos de recursos (melhor balanceamento)
local RESOURCE_TYPES = {
    {
        name = "Stone",
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Rock,
        size = Vector3.new(4, 4, 4),
        value = 10,
        spawnWeight = 0.5  -- 50% - mais comum
    },
    {
        name = "Metal",
        color = BrickColor.new("Really black"),
        material = Enum.Material.Metal,
        size = Vector3.new(3, 3, 3),
        value = 15,
        spawnWeight = 0.3  -- 30% - comum
    },
    {
        name = "<PERSON>",
        color = BrickColor.new("<PERSON>"),
        material = Enum.Material.Wood,
        size = Vector3.new(2, 6, 2),
        value = 12,
        spawnWeight = 0.15  -- 15% - menos comum
    },
    {
        name = "Crystal",
        color = BrickColor.new("Cyan"),
        material = Enum.Material.Neon,
        size = Vector3.new(2, 5, 2),
        value = 25,
        spawnWeight = 0.05  -- 5% - raro
    }
}

-- Áreas de spawn (reduzidas em 30% para acompanhar o mapa menor)
local SPAWN_AREAS = {
    {center = Vector3.new(0, 0, 0), radius = 280},
    {center = Vector3.new(140, 0, 140), radius = 105},
    {center = Vector3.new(-140, 0, -140), radius = 105},
    {center = Vector3.new(140, 0, -140), radius = 105},
    {center = Vector3.new(-140, 0, 140), radius = 105}
}

local activeResources = {}

-- Função para escolher tipo de recurso baseado no peso
local function chooseResourceType()
    local totalWeight = 0
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        totalWeight = totalWeight + resourceType.spawnWeight
    end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        currentWeight = currentWeight + resourceType.spawnWeight
        if random <= currentWeight then
            return resourceType
        end
    end
    
    return RESOURCE_TYPES[1] -- fallback
end

-- Função para encontrar posição válida para spawn (apenas no chão)
local function findValidSpawnPosition(area)
    local attempts = 0
    local maxAttempts = 30
    
    while attempts < maxAttempts do
        local angle = math.random() * math.pi * 2
        local distance = math.random() * area.radius
        local x = area.center.X + math.cos(angle) * distance
        local z = area.center.Z + math.sin(angle) * distance
        
        -- Raycast para encontrar o chão, ignorando barreiras e construções
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        
        -- Lista de objetos a ignorar (barreiras, construções, etc.)
        local ignoreList = {}
        for _, obj in ipairs(workspace:GetChildren()) do
            if obj.Name:match("Base_") then
                -- Ignora barreiras das bases
                local barrier = obj:FindFirstChild("Barrier")
                if barrier then
                    table.insert(ignoreList, barrier)
                end
            elseif obj.Name:match("_Building$") then
                -- Ignora construções
                table.insert(ignoreList, obj)
            end
        end
        
        raycastParams.FilterDescendantsInstances = ignoreList
        
        local raycastResult = workspace:Raycast(
            Vector3.new(x, 200, z),  -- Começa mais alto
            Vector3.new(0, -400, 0), -- Vai mais fundo
            raycastParams
        )
        
        if raycastResult then
            local y = raycastResult.Position.Y
            local position = Vector3.new(x, y + 2, z)
            
            -- Verifica se está realmente no chão (Y baixo)
            if y <= 5 then  -- Apenas posições próximas ao nível do chão
                -- Verifica se não há outros recursos muito próximos
                local tooClose = false
                for _, resource in ipairs(activeResources) do
                    if resource and resource.Parent then
                        local distance = (resource.Position - position).Magnitude
                        if distance < 10 then
                            tooClose = true
                            break
                        end
                    end
                end
                
                -- Verifica se não está muito próximo de bases
                local tooCloseToBase = false
                for _, base in ipairs(workspace:GetChildren()) do
                    if base.Name:match("Base_") then
                        local basePlatform = base:FindFirstChild("BasePlatform")
                        if basePlatform then
                            local distanceToBase = (basePlatform.Position - position).Magnitude
                            if distanceToBase < 30 then  -- Mantém distância das bases
                                tooCloseToBase = true
                                break
                            end
                        end
                    end
                end
                
                if not tooClose and not tooCloseToBase then
                    return position
                end
            end
        end
        
        attempts = attempts + 1
    end
    
    return nil
end

-- Cria um recurso com animação de spawn e timer de despawn
local function createResource(resourceType, position)
    local resource = Instance.new("Part")
    resource.Name = resourceType.name .. "Resource"
    resource.Size = Vector3.new(0.1, 0.1, 0.1) -- Começa muito pequeno
    resource.Position = Vector3.new(position.X, position.Y - 2, position.Z) -- Começa no subsolo
    resource.BrickColor = resourceType.color
    resource.Material = resourceType.material
    resource.Shape = Enum.PartType.Block
    resource.Anchored = true
    resource.Parent = workspace

    -- Valores do recurso (conforme especificação)
    local originalSize = Instance.new("Vector3Value")
    originalSize.Name = "OriginalSize"
    originalSize.Value = resourceType.size
    originalSize.Parent = resource

    local resourceTypeValue = Instance.new("StringValue")
    resourceTypeValue.Name = "ResourceType"
    resourceTypeValue.Value = resourceType.name
    resourceTypeValue.Parent = resource

    local resourceValue = Instance.new("NumberValue")
    resourceValue.Name = "ResourceValue"
    resourceValue.Value = resourceType.value
    resourceValue.Parent = resource

    -- Timestamp de criação para controle de despawn
    local spawnTime = Instance.new("NumberValue")
    spawnTime.Name = "SpawnTime"
    spawnTime.Value = tick()
    spawnTime.Parent = resource

    -- Efeitos visuais de spawn melhorados
    local function createSpawnEffects()
        -- Partículas de terra voando (mais partículas)
        for i = 1, 12 do
            local particle = Instance.new("Part")
            particle.Name = "SpawnParticle"
            particle.Size = Vector3.new(
                math.random(3, 8) / 10,
                math.random(3, 8) / 10,
                math.random(3, 8) / 10
            )
            particle.Position = position + Vector3.new(
                math.random(-4, 4),
                math.random(-1, 1),
                math.random(-4, 4)
            )
            particle.BrickColor = BrickColor.new("Brown")
            particle.Material = Enum.Material.Rock
            particle.CanCollide = false
            particle.Anchored = false
            particle.Parent = workspace

            -- Aplica força para cima com mais variação
            local bodyVelocity = Instance.new("BodyVelocity")
            bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
            bodyVelocity.Velocity = Vector3.new(
                math.random(-15, 15),
                math.random(20, 35),
                math.random(-15, 15)
            )
            bodyVelocity.Parent = particle

            -- Remove partícula após tempo variável
            game:GetService("Debris"):AddItem(particle, math.random(15, 30) / 10)
        end

        -- Efeito de luz pulsante
        local light = Instance.new("PointLight")
        light.Brightness = 3
        light.Range = 15
        light.Color = resourceType.color.Color
        light.Parent = resource

        -- Animação de luz pulsante
        spawn(function()
            for i = 1, 20 do
                if light and light.Parent then
                    light.Brightness = 3 + math.sin(i * 0.5) * 1.5
                    wait(0.1)
                end
            end
            if light and light.Parent then
                light.Brightness = 1
            end
        end)

        -- Efeito de brilho no recurso
        local selectionBox = Instance.new("SelectionBox")
        selectionBox.Adornee = resource
        selectionBox.Color3 = resourceType.color.Color
        selectionBox.LineThickness = 0.2
        selectionBox.Transparency = 0.3
        selectionBox.Parent = resource

        -- Remove efeitos após 5 segundos
        spawn(function()
            wait(5)
            if selectionBox and selectionBox.Parent then
                selectionBox:Destroy()
            end
        end)
    end

    -- Animação de crescimento e subida melhorada
    local function animateSpawn()
        createSpawnEffects()

        local startTime = tick()
        local duration = 2.5 -- 2.5 segundos de animação
        local startPos = resource.Position
        local targetPos = position
        local startSize = resource.Size
        local targetSize = resourceType.size

        local connection
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local elapsed = tick() - startTime
            local progress = math.min(elapsed / duration, 1)

            -- Função de easing melhorada (elastic out)
            local easedProgress = progress
            if progress < 1 then
                local c4 = (2 * math.pi) / 3
                easedProgress = math.pow(2, -10 * progress) * math.sin((progress * 10 - 0.75) * c4) + 1
            end

            -- Interpola posição
            resource.Position = startPos:lerp(targetPos, progress)

            -- Interpola tamanho com easing
            resource.Size = startSize:lerp(targetSize, easedProgress)

            -- Adiciona rotação durante spawn
            resource.CFrame = CFrame.new(resource.Position) * CFrame.Angles(0, elapsed * 2, 0)

            if progress >= 1 then
                connection:Disconnect()
                resource.Position = targetPos
                resource.Size = targetSize
                resource.CFrame = CFrame.new(targetPos)
            end
        end)
    end

    -- Efeito de despawn após 1 minuto
    spawn(function()
        wait(GameConfig.RESOURCE_CONFIG.DESPAWN_TIME)
        if resource and resource.Parent then
            -- Efeito de despawn
            local function createDespawnEffect()
                -- Partículas de desaparecimento
                for i = 1, 8 do
                    local particle = Instance.new("Part")
                    particle.Name = "DespawnParticle"
                    particle.Size = Vector3.new(0.3, 0.3, 0.3)
                    particle.Position = resource.Position + Vector3.new(
                        math.random(-2, 2),
                        math.random(0, 3),
                        math.random(-2, 2)
                    )
                    particle.BrickColor = resourceType.color
                    particle.Material = Enum.Material.Neon
                    particle.CanCollide = false
                    particle.Anchored = false
                    particle.Transparency = 0.5
                    particle.Parent = workspace

                    -- Movimento para cima e fade
                    local bodyVelocity = Instance.new("BodyVelocity")
                    bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
                    bodyVelocity.Velocity = Vector3.new(0, 10, 0)
                    bodyVelocity.Parent = particle

                    -- Fade out
                    spawn(function()
                        for j = 1, 20 do
                            if particle and particle.Parent then
                                particle.Transparency = 0.5 + (j / 20) * 0.5
                                wait(0.05)
                            end
                        end
                        if particle and particle.Parent then
                            particle:Destroy()
                        end
                    end)
                end

                -- Flash de luz
                local light = Instance.new("PointLight")
                light.Brightness = 5
                light.Range = 20
                light.Color = resourceType.color.Color
                light.Parent = resource

                spawn(function()
                    wait(0.5)
                    if light and light.Parent then
                        light:Destroy()
                    end
                end)
            end

            createDespawnEffect()
            
            -- Remove da lista de recursos ativos
            for i = #activeResources, 1, -1 do
                if activeResources[i] == resource then
                    table.remove(activeResources, i)
                    break
                end
            end
            
            -- Destroi o recurso após efeito
            wait(1)
            if resource and resource.Parent then
                resource:Destroy()
            end
        end
    end)

    -- Inicia animação após um pequeno delay
    spawn(function()
        wait(0.1)
        animateSpawn()
    end)

    -- Adiciona à lista de recursos ativos
    table.insert(activeResources, resource)

    return resource
end

-- Não spawna recursos iniciais - apenas inicia o sistema
local function spawnInitialResources()
    -- Não spawna nenhum recurso inicial conforme solicitado
    print("Sistema de recursos iniciado - aguardando spawn automático")
end

-- Sistema de spawn contínuo e frequente
local function continuousSpawning()
    while true do
        wait(GameConfig.RESOURCE_CONFIG.SPAWN_FREQUENCY)
        
        -- Remove recursos destruídos da lista
        for i = #activeResources, 1, -1 do
            if not activeResources[i] or not activeResources[i].Parent then
                table.remove(activeResources, i)
            end
        end
        
        -- Spawna novos recursos com frequência se abaixo do máximo
        if #activeResources < GameConfig.RESOURCE_CONFIG.MAX_RESOURCES then
            -- Spawna 1-3 recursos por vez para manter atividade constante
            local spawnCount = math.random(1, 3)
            local spawned = 0
            
            for i = 1, spawnCount do
                if #activeResources < GameConfig.RESOURCE_CONFIG.MAX_RESOURCES then
                    local area = SPAWN_AREAS[math.random(1, #SPAWN_AREAS)]
                    local position = findValidSpawnPosition(area)
                    
                    if position then
                        local resourceType = chooseResourceType()
                        createResource(resourceType, position)
                        spawned = spawned + 1
                    end
                end
            end
            
            if spawned > 0 then
                print("Spawned " .. spawned .. " new resources (Total: " .. #activeResources .. ")")
            end
        end
    end
end

-- Sistema de limpeza periódica (backup)
local function periodicCleanup()
    while true do
        wait(GameConfig.RESOURCE_CONFIG.RESPAWN_TIME)
        
        -- Remove recursos muito antigos que não foram coletados
        local currentTime = tick()
        for i = #activeResources, 1, -1 do
            local resource = activeResources[i]
            if resource and resource.Parent then
                local spawnTime = resource:FindFirstChild("SpawnTime")
                if spawnTime and (currentTime - spawnTime.Value) > (GameConfig.RESOURCE_CONFIG.DESPAWN_TIME + 30) then
                    -- Recurso muito antigo, força remoção
                    resource:Destroy()
                    table.remove(activeResources, i)
                end
            else
                table.remove(activeResources, i)
            end
        end
        
        print("Cleanup completed. Active resources: " .. #activeResources)
    end
end

-- Inicializa o sistema melhorado
spawn(function()
    spawnInitialResources()
    continuousSpawning()
end)

-- Inicia sistema de limpeza
spawn(function()
    periodicCleanup()
end)

print("ResourceManager inicializado com sucesso!")

return ResourceManager