-- ResourceManager.lua
-- Gerencia recursos coletáveis no mapa

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Carrega configurações
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local ResourceManager = {}

-- Tipos de recursos (melhor balanceamento)
local RESOURCE_TYPES = {
    {
        name = "Stone",
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Rock,
        size = Vector3.new(4, 4, 4),
        value = 10,
        spawnWeight = 0.5  -- 50% - mais comum
    },
    {
        name = "Metal",
        color = BrickColor.new("Really black"),
        material = Enum.Material.Metal,
        size = Vector3.new(3, 3, 3),
        value = 15,
        spawnWeight = 0.3  -- 30% - comum
    },
    {
        name = "<PERSON>",
        color = BrickColor.new("<PERSON>"),
        material = Enum.Material.Wood,
        size = Vector3.new(2, 6, 2),
        value = 12,
        spawnWeight = 0.15  -- 15% - menos comum
    },
    {
        name = "Crystal",
        color = BrickColor.new("Cyan"),
        material = Enum.Material.Neon,
        size = Vector3.new(2, 5, 2),
        value = 25,
        spawnWeight = 0.05  -- 5% - raro
    }
}

-- Áreas de spawn
local SPAWN_AREAS = {
    {center = Vector3.new(0, 0, 0), radius = 400},
    {center = Vector3.new(200, 0, 200), radius = 150},
    {center = Vector3.new(-200, 0, -200), radius = 150},
    {center = Vector3.new(200, 0, -200), radius = 150},
    {center = Vector3.new(-200, 0, 200), radius = 150}
}

local activeResources = {}

-- Função para escolher tipo de recurso baseado no peso
local function chooseResourceType()
    local totalWeight = 0
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        totalWeight = totalWeight + resourceType.spawnWeight
    end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        currentWeight = currentWeight + resourceType.spawnWeight
        if random <= currentWeight then
            return resourceType
        end
    end
    
    return RESOURCE_TYPES[1] -- fallback
end

-- Função para encontrar posição válida para spawn
local function findValidSpawnPosition(area)
    local attempts = 0
    local maxAttempts = 20
    
    while attempts < maxAttempts do
        local angle = math.random() * math.pi * 2
        local distance = math.random() * area.radius
        local x = area.center.X + math.cos(angle) * distance
        local z = area.center.Z + math.sin(angle) * distance
        
        -- Raycast para encontrar o chão
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {}
        
        local raycastResult = workspace:Raycast(
            Vector3.new(x, 100, z),
            Vector3.new(0, -200, 0),
            raycastParams
        )
        
        if raycastResult then
            local y = raycastResult.Position.Y
            local position = Vector3.new(x, y + 2, z)
            
            -- Verifica se não há outros recursos muito próximos
            local tooClose = false
            for _, resource in ipairs(activeResources) do
                if resource and resource.Parent then
                    local distance = (resource.Position - position).Magnitude
                    if distance < 10 then
                        tooClose = true
                        break
                    end
                end
            end
            
            if not tooClose then
                return position
            end
        end
        
        attempts = attempts + 1
    end
    
    return nil
end

-- Cria um recurso com animação de spawn
local function createResource(resourceType, position)
    local resource = Instance.new("Part")
    resource.Name = resourceType.name .. "Resource"
    resource.Size = Vector3.new(0.1, 0.1, 0.1) -- Começa muito pequeno
    resource.Position = Vector3.new(position.X, position.Y - 2, position.Z) -- Começa no subsolo
    resource.BrickColor = resourceType.color
    resource.Material = resourceType.material
    resource.Shape = Enum.PartType.Block
    resource.Anchored = true
    resource.Parent = workspace

    -- Valores do recurso (conforme especificação)
    local originalSize = Instance.new("Vector3Value")
    originalSize.Name = "OriginalSize"
    originalSize.Value = resourceType.size
    originalSize.Parent = resource

    local resourceTypeValue = Instance.new("StringValue")
    resourceTypeValue.Name = "ResourceType"
    resourceTypeValue.Value = resourceType.name
    resourceTypeValue.Parent = resource

    local resourceValue = Instance.new("NumberValue")
    resourceValue.Name = "ResourceValue"
    resourceValue.Value = resourceType.value
    resourceValue.Parent = resource

    -- Efeitos visuais de spawn
    local function createSpawnEffects()
        -- Partículas de terra voando
        for i = 1, 8 do
            local particle = Instance.new("Part")
            particle.Name = "SpawnParticle"
            particle.Size = Vector3.new(0.5, 0.5, 0.5)
            particle.Position = position + Vector3.new(
                math.random(-3, 3),
                0,
                math.random(-3, 3)
            )
            particle.BrickColor = BrickColor.new("Brown")
            particle.Material = Enum.Material.Rock
            particle.CanCollide = false
            particle.Anchored = false
            particle.Parent = workspace

            -- Aplica força para cima
            local bodyVelocity = Instance.new("BodyVelocity")
            bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
            bodyVelocity.Velocity = Vector3.new(
                math.random(-10, 10),
                math.random(15, 25),
                math.random(-10, 10)
            )
            bodyVelocity.Parent = particle

            -- Remove partícula após 2 segundos
            game:GetService("Debris"):AddItem(particle, 2)
        end

        -- Efeito de luz
        local light = Instance.new("PointLight")
        light.Brightness = 2
        light.Range = 10
        light.Color = resourceType.color.Color
        light.Parent = resource

        -- Remove luz após animação
        spawn(function()
            wait(3)
            if light and light.Parent then
                light:Destroy()
            end
        end)
    end

    -- Animação de crescimento e subida
    local function animateSpawn()
        createSpawnEffects()

        local startTime = tick()
        local duration = 2 -- 2 segundos de animação
        local startPos = resource.Position
        local targetPos = position
        local startSize = resource.Size
        local targetSize = resourceType.size

        local connection
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local elapsed = tick() - startTime
            local progress = math.min(elapsed / duration, 1)

            -- Função de easing (bounce out)
            local easedProgress = progress
            if progress < 1 then
                easedProgress = 1 - math.pow(1 - progress, 3)
            end

            -- Interpola posição
            resource.Position = startPos:lerp(targetPos, easedProgress)

            -- Interpola tamanho
            resource.Size = startSize:lerp(targetSize, easedProgress)

            -- Adiciona um pequeno bounce
            if progress < 0.8 then
                local bounce = math.sin(progress * math.pi * 4) * 0.5 * (1 - progress)
                resource.Position = resource.Position + Vector3.new(0, bounce, 0)
            end

            if progress >= 1 then
                connection:Disconnect()
                resource.Position = targetPos
                resource.Size = targetSize
            end
        end)
    end

    -- Inicia animação após um pequeno delay
    spawn(function()
        wait(0.1)
        animateSpawn()
    end)

    -- Adiciona à lista de recursos ativos
    table.insert(activeResources, resource)

    return resource
end

-- Spawna recursos iniciais
local function spawnInitialResources()
    for i = 1, GameConfig.RESOURCE_CONFIG.MAX_RESOURCES do
        local area = SPAWN_AREAS[math.random(1, #SPAWN_AREAS)]
        local position = findValidSpawnPosition(area)
        
        if position then
            local resourceType = chooseResourceType()
            createResource(resourceType, position)
        end
    end
    
    print("Spawned " .. #activeResources .. " initial resources")
end

-- Respawna recursos periodicamente
local function respawnResources()
    while true do
        wait(GameConfig.RESOURCE_CONFIG.RESPAWN_TIME)
        
        -- Remove recursos destruídos da lista
        for i = #activeResources, 1, -1 do
            if not activeResources[i] or not activeResources[i].Parent then
                table.remove(activeResources, i)
            end
        end
        
        -- Spawna novos recursos se necessário
        local resourcesNeeded = GameConfig.RESOURCE_CONFIG.MAX_RESOURCES - #activeResources
        for i = 1, resourcesNeeded do
            local area = SPAWN_AREAS[math.random(1, #SPAWN_AREAS)]
            local position = findValidSpawnPosition(area)
            
            if position then
                local resourceType = chooseResourceType()
                createResource(resourceType, position)
            end
        end
        
        if resourcesNeeded > 0 then
            print("Respawned " .. resourcesNeeded .. " resources")
        end
    end
end

-- Inicializa o sistema
spawn(function()
    spawnInitialResources()
    respawnResources()
end)

print("ResourceManager inicializado com sucesso!")

return ResourceManager