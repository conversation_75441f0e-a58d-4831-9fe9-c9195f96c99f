-- SpawnManager.lua
-- Gerencia spawn dos jogadores em bases não reivindicadas

local Players = game:GetService("Players")

local SpawnManager = {}

-- Função para encontrar uma base não reivindicada
local function findUnclaimedBase()
    local unclaimedBases = {}

    for _, base in ipairs(workspace:Get<PERSON><PERSON>dren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            -- Base é não reivindicada se não tem owner OU se tem owner mas não tem partner
            if owner and (not owner.Value or (owner.Value and partner and not partner.Value)) then
                table.insert(unclaimedBases, base)
            end
        end
    end

    -- Retorna uma base aleatória das disponíveis
    if #unclaimedBases > 0 then
        return unclaimedBases[math.random(1, #unclaimedBases)]
    end

    return nil
end

-- Função para criar spawn temporário perto de uma base
local function createTempSpawn(base)
    local spawnLocation = Instance.new("SpawnLocation")
    spawnLocation.Name = "TempSpawn_" .. base.Name
    spawnLocation.Size = Vector3.new(8, 1, 8)
    spawnLocation.BrickColor = BrickColor.new("Bright green")
    spawnLocation.Material = Enum.Material.ForceField
    spawnLocation.Anchored = true
    spawnLocation.Parent = workspace

    -- Encontra a posição da base (procura por ClaimPad ou usa posição central)
    local basePosition = Vector3.new(0, 0, 0)
    local claimPad = base:FindFirstChild("ClaimPad")
    if claimPad then
        basePosition = claimPad.Position
    elseif base.PrimaryPart then
        basePosition = base.PrimaryPart.Position
    else
        -- Calcula posição média dos filhos da base
        local totalPos = Vector3.new(0, 0, 0)
        local count = 0
        for _, child in ipairs(base:GetChildren()) do
            if child:IsA("BasePart") then
                totalPos = totalPos + child.Position
                count = count + 1
            end
        end
        if count > 0 then
            basePosition = totalPos / count
        end
    end

    -- Posiciona o spawn a uma distância segura da base
    spawnLocation.Position = basePosition + Vector3.new(0, 2, 25)

    -- Adiciona texto informativo
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = spawnLocation

    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "🎯 SPAWN PERTO DE " .. base.Name
    textLabel.TextColor3 = Color3.new(0, 0, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui

    -- Adiciona efeito de brilho
    local pointLight = Instance.new("PointLight")
    pointLight.Color = Color3.new(0, 1, 0)
    pointLight.Brightness = 2
    pointLight.Range = 20
    pointLight.Parent = spawnLocation

    return spawnLocation
end

-- Função para configurar spawn do jogador
local function setupPlayerSpawn(player)
    -- Encontra uma base não reivindicada
    local unclaimedBase = findUnclaimedBase()

    if unclaimedBase then
        -- Cria spawn temporário perto da base
        local tempSpawn = createTempSpawn(unclaimedBase)
        player.RespawnLocation = tempSpawn

        print("Jogador " .. player.Name .. " spawnará perto da " .. unclaimedBase.Name)

        -- Remove spawn temporário após 60 segundos
        spawn(function()
            wait(60)
            if tempSpawn and tempSpawn.Parent then
                tempSpawn:Destroy()
            end
        end)
    else
        -- Se não há bases disponíveis, usa spawn central
        local centralSpawn = workspace:FindFirstChild("CentralSpawn")
        if centralSpawn then
            player.RespawnLocation = centralSpawn
            print("Jogador " .. player.Name .. " spawnará no centro (sem bases disponíveis)")
        end
    end
end

-- Remove spawn central se existir
local centralSpawn = workspace:FindFirstChild("CentralSpawn")
if centralSpawn then
    centralSpawn:Destroy()
    print("Spawn central removido - usando sistema de bases")
end

-- Conecta aos novos jogadores
Players.PlayerAdded:Connect(function(player)
    setupPlayerSpawn(player)

    -- Redefine spawn quando o jogador spawna
    player.CharacterAdded:Connect(function()
        -- Se o jogador não tem base, configura spawn temporário
        local hasBase = false
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")

                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    hasBase = true
                    break
                end
            end
        end

        if not hasBase then
            setupPlayerSpawn(player)
        end
    end)
end)

-- Configura spawn para jogadores já conectados
for _, player in ipairs(Players:GetPlayers()) do
    setupPlayerSpawn(player)
end

print("SpawnManager inicializado - jogadores spawnarão perto de bases não reivindicadas!")

return SpawnManager