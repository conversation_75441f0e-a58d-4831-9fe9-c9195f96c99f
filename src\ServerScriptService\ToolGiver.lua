-- ToolGiver.lua
-- Garante que todos os jogadores recebam as ferramentas

local Players = game:GetService("Players")
local StarterPack = game:GetService("StarterPack")

-- Função para dar ferramentas a um jogador
local function giveToolsToPlayer(player)
    if not player.Character then return end
    
    local backpack = player:FindFirstChild("Backpack")
    if not backpack then return end
    
    -- Verifica se já tem as ferramentas
    local hasCombat = backpack:FindFirstChild("CombatGun") or player.Character:FindFirstChild("CombatGun")
    local hasCollector = backpack:FindFirstChild("CollectorGun") or player.Character:FindFirstChild("CollectorGun")
    
    -- Dá CombatGun se não tiver
    if not hasCombat then
        local combatGun = StarterPack:FindFirstChild("CombatGun")
        if combatGun then
            local combatClone = combatGun:Clone()
            combatClone.Parent = backpack
            print("✅ CombatGun dada para " .. player.Name)
        else
            warn("❌ CombatGun não encontrada no StarterPack")
        end
    end
    
    -- Dá CollectorGun se não tiver
    if not hasCollector then
        local collectorGun = StarterPack:FindFirstChild("CollectorGun")
        if collectorGun then
            local collectorClone = collectorGun:Clone()
            collectorClone.Parent = backpack
            print("✅ CollectorGun dada para " .. player.Name)
        else
            warn("❌ CollectorGun não encontrada no StarterPack")
        end
    end
end

-- Conecta aos jogadores que entram
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function()
        wait(2) -- Aguarda um pouco para garantir que tudo carregou
        giveToolsToPlayer(player)
    end)
end)

-- Dá ferramentas aos jogadores já conectados
for _, player in ipairs(Players:GetPlayers()) do
    if player.Character then
        giveToolsToPlayer(player)
    end
    
    player.CharacterAdded:Connect(function()
        wait(2)
        giveToolsToPlayer(player)
    end)
end

print("✅ ToolGiver inicializado - jogadores receberão ferramentas automaticamente!")

return true