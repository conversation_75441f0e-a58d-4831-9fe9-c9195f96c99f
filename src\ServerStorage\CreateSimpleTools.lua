-- CreateSimpleTools.lua
-- Cria ferramentas simples que funcionam

local StarterPack = game:GetService("StarterPack")

local function createSimpleTools()
    -- Remove ferramentas existentes
    local existingCombat = StarterPack:FindFirstChild("CombatGun")
    local existingCollector = StarterPack:FindFirstChild("CollectorGun")

    if existingCombat then existingCombat:Destroy() end
    if existingCollector then existingCollector:Destroy() end

    -- 1. CombatGun
    local combatGun = Instance.new("Tool")
    combatGun.Name = "CombatGun"
    combatGun.RequiresHandle = true
    combatGun.Parent = StarterPack

    -- Handle da CombatGun
    local combatHandle = Instance.new("Part")
    combatHandle.Name = "Handle"
    combatHandle.Size = Vector3.new(0.5, 0.5, 3)
    combatHandle.BrickColor = BrickColor.new("Really black")
    combatHandle.Material = Enum.Material.Metal
    combatHandle.Parent = combatGun

    -- Mesh da CombatGun
    local combatMesh = Instance.new("BlockMesh")
    combatMesh.Scale = Vector3.new(0.8, 0.8, 1.2)
    combatMesh.Parent = combatHandle

    -- Copia script da CombatGun se existir
    local combatScript = StarterPack:FindFirstChild("CombatGunScript")
    if combatScript then
        local newCombatScript = combatScript:Clone()
        newCombatScript.Parent = combatGun
        print("Script da CombatGun anexado")
    else
        warn("CombatGunScript não encontrado no StarterPack")
    end

    -- 2. CollectorGun
    local collectorGun = Instance.new("Tool")
    collectorGun.Name = "CollectorGun"
    collectorGun.RequiresHandle = true
    collectorGun.Parent = StarterPack

    -- Handle da CollectorGun
    local collectorHandle = Instance.new("Part")
    collectorHandle.Name = "Handle"
    collectorHandle.Size = Vector3.new(0.5, 0.5, 3)
    collectorHandle.BrickColor = BrickColor.new("Bright blue")
    collectorHandle.Material = Enum.Material.Neon
    collectorHandle.Parent = collectorGun

    -- Mesh da CollectorGun
    local collectorMesh = Instance.new("BlockMesh")
    collectorMesh.Scale = Vector3.new(0.8, 0.8, 1.2)
    collectorMesh.Parent = collectorHandle

    -- Copia script da CollectorGun se existir
    local collectorScript = StarterPack:FindFirstChild("CollectorGunScript")
    if collectorScript then
        local newCollectorScript = collectorScript:Clone()
        newCollectorScript.Parent = collectorGun
        print("Script da CollectorGun anexado")
    else
        warn("CollectorGunScript não encontrado no StarterPack")
    end

    print("Ferramentas com scripts criadas com sucesso!")
    return true
end

-- Executa a criação
createSimpleTools()

return true