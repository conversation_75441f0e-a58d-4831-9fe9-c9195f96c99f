-- CreateToolsWithScripts.lua
-- Cria ferramentas com LocalScripts anexados diretamente

local StarterPack = game:GetService("StarterPack")
local Players = game:GetService("Players")

local function createToolsWithScripts()
    -- Remove ferramentas existentes
    for _, tool in ipairs(StarterPack:GetChildren()) do
        if tool:IsA("Tool") then
            tool:Destroy()
        end
    end

    -- === COMBAT GUN ===
    local combatGun = Instance.new("Tool")
    combatGun.Name = "CombatGun"
    combatGun.RequiresHandle = true
    combatGun.CanBeDropped = false
    combatGun.Parent = StarterPack

    -- Handle da CombatGun
    local combatHandle = Instance.new("Part")
    combatHandle.Name = "Handle"
    combatHandle.Size = Vector3.new(0.5, 0.5, 3)
    combatHandle.BrickColor = BrickColor.new("Really black")
    combatHandle.Material = Enum.Material.Metal
    combatHandle.CanCollide = false
    combatHandle.Parent = combatGun

    -- Mesh da CombatGun
    local combatMesh = Instance.new("BlockMesh")
    combatMesh.Scale = Vector3.new(0.8, 0.8, 1.2)
    combatMesh.Parent = combatHandle

    -- LocalScript da CombatGun
    local combatScript = Instance.new("LocalScript")
    combatScript.Name = "CombatScript"
    combatScript.Parent = combatGun

    -- === COLLECTOR GUN ===
    local collectorGun = Instance.new("Tool")
    collectorGun.Name = "CollectorGun"
    collectorGun.RequiresHandle = true
    collectorGun.CanBeDropped = false
    collectorGun.Parent = StarterPack

    -- Handle da CollectorGun
    local collectorHandle = Instance.new("Part")
    collectorHandle.Name = "Handle"
    collectorHandle.Size = Vector3.new(0.5, 0.5, 3)
    collectorHandle.BrickColor = BrickColor.new("Bright blue")
    collectorHandle.Material = Enum.Material.Neon
    collectorHandle.CanCollide = false
    collectorHandle.Parent = collectorGun

    -- Mesh da CollectorGun
    local collectorMesh = Instance.new("BlockMesh")
    collectorMesh.Scale = Vector3.new(0.8, 0.8, 1.2)
    collectorMesh.Parent = collectorHandle

    -- LocalScript da CollectorGun
    local collectorScript = Instance.new("LocalScript")
    collectorScript.Name = "CollectorScript"
    collectorScript.Parent = collectorGun

    print("✅ Ferramentas com LocalScripts criadas!")
    return true
end

-- Função para dar ferramentas aos jogadores existentes
local function giveToolsToExistingPlayers()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character then
            local backpack = player:FindFirstChild("Backpack")
            if backpack then
                -- Verifica se já tem as ferramentas
                local hasCombat = backpack:FindFirstChild("CombatGun") or player.Character:FindFirstChild("CombatGun")
                local hasCollector = backpack:FindFirstChild("CollectorGun") or player.Character:FindFirstChild("CollectorGun")
                
                if not hasCombat and StarterPack:FindFirstChild("CombatGun") then
                    local combatClone = StarterPack.CombatGun:Clone()
                    combatClone.Parent = backpack
                    print("✅ CombatGun dada para " .. player.Name)
                end
                
                if not hasCollector and StarterPack:FindFirstChild("CollectorGun") then
                    local collectorClone = StarterPack.CollectorGun:Clone()
                    collectorClone.Parent = backpack
                    print("✅ CollectorGun dada para " .. player.Name)
                end
            end
        end
    end
end

-- Executa a criação
createToolsWithScripts()

-- Aguarda um pouco e dá ferramentas aos jogadores existentes
wait(1)
giveToolsToExistingPlayers()

return true