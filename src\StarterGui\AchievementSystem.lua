-- AchievementSystem.lua
-- Sistema de conquistas e progressão do jogador

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Variáveis da UI
local screenGui = nil
local achievementFrame = nil
local progressFrame = nil
local isAchievementMenuOpen = false

-- Dados do jogador
local playerStats = {
    -- Estatísticas básicas
    timePlayed = 0,
    basesOwned = 0,
    resourcesCollected = 0,
    resourcesDeposited = 0,
    buildingMaterials = 0,
    
    -- Combate
    kills = 0,
    deaths = 0,
    damageDealt = 0,
    damageTaken = 0,
    
    -- Bases
    basesDestroyed = 0,
    baseAttacks = 0,
    baseDefenses = 0,
    
    -- Social
    partnersInvited = 0,
    partnersAccepted = 0,
    teamsFormed = 0,
    
    -- Construção
    structuresBuilt = 0,
    structuresDestroyed = 0,
    materialsSpent = 0,
    
    -- Especiais
    dashesUsed = 0,
    timeInBarrier = 0,
    longestSurvival = 0
}

-- Definição das conquistas
local achievements = {
    -- Iniciante
    {
        id = "welcome",
        name = "Bem-vindo à Arena!",
        description = "Entre no jogo pela primeira vez",
        icon = "🎮",
        unlocked = true,
        progress = 1,
        maxProgress = 1,
        reward = "Título: Novato"
    },
    {
        id = "first_base",
        name = "Primeira Base",
        description = "Reivindique sua primeira base",
        icon = "🏠",
        unlocked = false,
        progress = 0,
        maxProgress = 1,
        reward = "XP: 100"
    },
    {
        id = "first_kill",
        name = "Primeiro Sangue",
        description = "Elimine seu primeiro inimigo",
        icon = "⚔️",
        unlocked = false,
        progress = 0,
        maxProgress = 1,
        reward = "Título: Guerreiro"
    },
    {
        id = "resource_collector",
        name = "Coletor de Recursos",
        description = "Colete 50 recursos",
        icon = "💎",
        unlocked = false,
        progress = 0,
        maxProgress = 50,
        reward = "XP: 200"
    },
    {
        id = "team_player",
        name = "Jogador de Equipe",
        description = "Forme uma dupla com outro jogador",
        icon = "🤝",
        unlocked = false,
        progress = 0,
        maxProgress = 1,
        reward = "Título: Companheiro"
    },
    
    -- Intermediário
    {
        id = "builder",
        name = "Construtor",
        description = "Construa 10 estruturas",
        icon = "🔨",
        unlocked = false,
        progress = 0,
        maxProgress = 10,
        reward = "XP: 300"
    },
    {
        id = "survivor",
        name = "Sobrevivente",
        description = "Sobreviva por 10 minutos",
        icon = "⏰",
        unlocked = false,
        progress = 0,
        maxProgress = 600, -- 10 minutos em segundos
        reward = "Título: Sobrevivente"
    },
    {
        id = "base_destroyer",
        name = "Destruidor de Bases",
        description = "Destrua 3 bases inimigas",
        icon = "💥",
        unlocked = false,
        progress = 0,
        maxProgress = 3,
        reward = "XP: 500"
    },
    
    -- Avançado
    {
        id = "veteran",
        name = "Veterano",
        description = "Jogue por 1 hora total",
        icon = "🏆",
        unlocked = false,
        progress = 0,
        maxProgress = 3600, -- 1 hora em segundos
        reward = "Título: Veterano"
    },
    {
        id = "master_builder",
        name = "Mestre Construtor",
        description = "Construa 50 estruturas",
        icon = "🏗️",
        unlocked = false,
        progress = 0,
        maxProgress = 50,
        reward = "XP: 1000"
    }
}

-- Função para criar a interface de conquistas
local function createAchievementUI()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("AchievementSystem")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "AchievementSystem"
    screenGui.Parent = playerGui
    
    -- Frame principal (inicialmente invisível)
    achievementFrame = Instance.new("Frame")
    achievementFrame.Name = "AchievementFrame"
    achievementFrame.Size = UDim2.new(0, 700, 0, 500)
    achievementFrame.Position = UDim2.new(0.5, -350, 0.5, -250)
    achievementFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    achievementFrame.BackgroundTransparency = 0.1
    achievementFrame.BorderSizePixel = 3
    achievementFrame.BorderColor3 = Color3.new(1, 0.8, 0)
    achievementFrame.Visible = false
    achievementFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = achievementFrame
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 50)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(1, 0.8, 0)
    titleLabel.Text = "🏆 CONQUISTAS E PROGRESSÃO"
    titleLabel.TextColor3 = Color3.new(0, 0, 0)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = achievementFrame
    
    -- Cantos arredondados para o título
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleLabel
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = achievementFrame
    
    -- Frame de estatísticas (lado esquerdo)
    local statsFrame = Instance.new("Frame")
    statsFrame.Size = UDim2.new(0.4, -10, 1, -100)
    statsFrame.Position = UDim2.new(0, 10, 0, 60)
    statsFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    statsFrame.BorderSizePixel = 1
    statsFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    statsFrame.Parent = achievementFrame
    
    -- Título das estatísticas
    local statsTitle = Instance.new("TextLabel")
    statsTitle.Size = UDim2.new(1, 0, 0, 30)
    statsTitle.Position = UDim2.new(0, 0, 0, 0)
    statsTitle.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    statsTitle.Text = "📊 ESTATÍSTICAS"
    statsTitle.TextColor3 = Color3.new(1, 1, 1)
    statsTitle.TextScaled = true
    statsTitle.Font = Enum.Font.SourceSansBold
    statsTitle.Parent = statsFrame
    
    -- ScrollingFrame para estatísticas
    local statsScroll = Instance.new("ScrollingFrame")
    statsScroll.Size = UDim2.new(1, -10, 1, -40)
    statsScroll.Position = UDim2.new(0, 5, 0, 35)
    statsScroll.BackgroundTransparency = 1
    statsScroll.ScrollBarThickness = 8
    statsScroll.CanvasSize = UDim2.new(0, 0, 0, 400)
    statsScroll.Parent = statsFrame
    
    -- Frame de conquistas (lado direito)
    local achievementsFrame = Instance.new("Frame")
    achievementsFrame.Size = UDim2.new(0.6, -10, 1, -100)
    achievementsFrame.Position = UDim2.new(0.4, 10, 0, 60)
    achievementsFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    achievementsFrame.BorderSizePixel = 1
    achievementsFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    achievementsFrame.Parent = achievementFrame
    
    -- Título das conquistas
    local achievementsTitle = Instance.new("TextLabel")
    achievementsTitle.Size = UDim2.new(1, 0, 0, 30)
    achievementsTitle.Position = UDim2.new(0, 0, 0, 0)
    achievementsTitle.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    achievementsTitle.Text = "🏆 CONQUISTAS"
    achievementsTitle.TextColor3 = Color3.new(1, 1, 1)
    achievementsTitle.TextScaled = true
    achievementsTitle.Font = Enum.Font.SourceSansBold
    achievementsTitle.Parent = achievementsFrame
    
    -- ScrollingFrame para conquistas
    local achievementsScroll = Instance.new("ScrollingFrame")
    achievementsScroll.Size = UDim2.new(1, -10, 1, -40)
    achievementsScroll.Position = UDim2.new(0, 5, 0, 35)
    achievementsScroll.BackgroundTransparency = 1
    achievementsScroll.ScrollBarThickness = 8
    achievementsScroll.CanvasSize = UDim2.new(0, 0, 0, #achievements * 80)
    achievementsScroll.Parent = achievementsFrame
    
    -- Conecta botão fechar
    closeButton.MouseButton1Click:Connect(function()
        toggleAchievementMenu()
    end)
    
    return statsScroll, achievementsScroll
end

-- Função para atualizar estatísticas na UI
local function updateStatsUI(statsScroll)
    -- Limpa conteúdo anterior
    for _, child in ipairs(statsScroll:GetChildren()) do
        if child:IsA("TextLabel") then
            child:Destroy()
        end
    end
    
    local yPos = 0
    local stats = {
        {"⏰ Tempo Jogado", string.format("%d:%02d", math.floor(playerStats.timePlayed / 60), playerStats.timePlayed % 60)},
        {"🏠 Bases Possuídas", tostring(playerStats.basesOwned)},
        {"💎 Recursos Coletados", tostring(playerStats.resourcesCollected)},
        {"📦 Recursos Depositados", tostring(playerStats.resourcesDeposited)},
        {"⚔️ Eliminações", tostring(playerStats.kills)},
        {"☠️ Mortes", tostring(playerStats.deaths)},
        {"💥 Dano Causado", tostring(playerStats.damageDealt)},
        {"🛡️ Dano Recebido", tostring(playerStats.damageTaken)},
        {"🏗️ Estruturas Construídas", tostring(playerStats.structuresBuilt)},
        {"⚡ Dashes Usados", tostring(playerStats.dashesUsed)},
        {"🤝 Convites Enviados", tostring(playerStats.partnersInvited)}
    }
    
    for _, stat in ipairs(stats) do
        local statLabel = Instance.new("TextLabel")
        statLabel.Size = UDim2.new(1, -10, 0, 25)
        statLabel.Position = UDim2.new(0, 5, 0, yPos)
        statLabel.BackgroundTransparency = 1
        statLabel.Text = stat[1] .. ": " .. stat[2]
        statLabel.TextColor3 = Color3.new(1, 1, 1)
        statLabel.TextScaled = true
        statLabel.TextXAlignment = Enum.TextXAlignment.Left
        statLabel.Font = Enum.Font.SourceSans
        statLabel.Parent = statsScroll
        
        yPos = yPos + 30
    end
end

-- Função para atualizar conquistas na UI
local function updateAchievementsUI(achievementsScroll)
    -- Limpa conteúdo anterior
    for _, child in ipairs(achievementsScroll:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    local yPos = 0
    
    for _, achievement in ipairs(achievements) do
        local achievementFrame = Instance.new("Frame")
        achievementFrame.Size = UDim2.new(1, -10, 0, 70)
        achievementFrame.Position = UDim2.new(0, 5, 0, yPos)
        achievementFrame.BackgroundColor3 = achievement.unlocked and Color3.new(0, 0.3, 0) or Color3.new(0.2, 0.2, 0.2)
        achievementFrame.BorderSizePixel = 1
        achievementFrame.BorderColor3 = achievement.unlocked and Color3.new(0, 1, 0) or Color3.new(0.5, 0.5, 0.5)
        achievementFrame.Parent = achievementsScroll
        
        -- Ícone
        local iconLabel = Instance.new("TextLabel")
        iconLabel.Size = UDim2.new(0, 50, 0, 50)
        iconLabel.Position = UDim2.new(0, 5, 0, 10)
        iconLabel.BackgroundTransparency = 1
        iconLabel.Text = achievement.icon
        iconLabel.TextColor3 = Color3.new(1, 1, 1)
        iconLabel.TextScaled = true
        iconLabel.Font = Enum.Font.SourceSansBold
        iconLabel.Parent = achievementFrame
        
        -- Nome e descrição
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(1, -120, 0, 25)
        nameLabel.Position = UDim2.new(0, 60, 0, 5)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = achievement.name
        nameLabel.TextColor3 = achievement.unlocked and Color3.new(1, 1, 0) or Color3.new(1, 1, 1)
        nameLabel.TextScaled = true
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.Parent = achievementFrame
        
        local descLabel = Instance.new("TextLabel")
        descLabel.Size = UDim2.new(1, -120, 0, 20)
        descLabel.Position = UDim2.new(0, 60, 0, 25)
        descLabel.BackgroundTransparency = 1
        descLabel.Text = achievement.description
        descLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
        descLabel.TextScaled = true
        descLabel.TextXAlignment = Enum.TextXAlignment.Left
        descLabel.Font = Enum.Font.SourceSans
        descLabel.Parent = achievementFrame
        
        -- Barra de progresso
        if achievement.maxProgress > 1 then
            local progressBG = Instance.new("Frame")
            progressBG.Size = UDim2.new(1, -120, 0, 8)
            progressBG.Position = UDim2.new(0, 60, 0, 50)
            progressBG.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
            progressBG.BorderSizePixel = 0
            progressBG.Parent = achievementFrame
            
            local progressBar = Instance.new("Frame")
            progressBar.Size = UDim2.new(achievement.progress / achievement.maxProgress, 0, 1, 0)
            progressBar.Position = UDim2.new(0, 0, 0, 0)
            progressBar.BackgroundColor3 = achievement.unlocked and Color3.new(0, 1, 0) or Color3.new(1, 1, 0)
            progressBar.BorderSizePixel = 0
            progressBar.Parent = progressBG
            
            local progressText = Instance.new("TextLabel")
            progressText.Size = UDim2.new(1, 0, 1, 0)
            progressText.BackgroundTransparency = 1
            progressText.Text = achievement.progress .. "/" .. achievement.maxProgress
            progressText.TextColor3 = Color3.new(1, 1, 1)
            progressText.TextScaled = true
            progressText.Font = Enum.Font.SourceSans
            progressText.Parent = progressBG
        end
        
        -- Status
        local statusLabel = Instance.new("TextLabel")
        statusLabel.Size = UDim2.new(0, 50, 0, 20)
        statusLabel.Position = UDim2.new(1, -55, 0, 5)
        statusLabel.BackgroundTransparency = 1
        statusLabel.Text = achievement.unlocked and "✅" or "🔒"
        statusLabel.TextColor3 = achievement.unlocked and Color3.new(0, 1, 0) or Color3.new(0.5, 0.5, 0.5)
        statusLabel.TextScaled = true
        statusLabel.Font = Enum.Font.SourceSansBold
        statusLabel.Parent = achievementFrame
        
        yPos = yPos + 75
    end
end

-- Função para verificar e desbloquear conquistas
local function checkAchievements()
    for _, achievement in ipairs(achievements) do
        if not achievement.unlocked then
            local shouldUnlock = false
            
            -- Verifica condições específicas
            if achievement.id == "first_base" and playerStats.basesOwned > 0 then
                achievement.progress = 1
                shouldUnlock = true
            elseif achievement.id == "first_kill" and playerStats.kills > 0 then
                achievement.progress = 1
                shouldUnlock = true
            elseif achievement.id == "resource_collector" then
                achievement.progress = math.min(playerStats.resourcesCollected, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            elseif achievement.id == "team_player" and playerStats.teamsFormed > 0 then
                achievement.progress = 1
                shouldUnlock = true
            elseif achievement.id == "builder" then
                achievement.progress = math.min(playerStats.structuresBuilt, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            elseif achievement.id == "survivor" then
                achievement.progress = math.min(playerStats.timePlayed, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            elseif achievement.id == "base_destroyer" then
                achievement.progress = math.min(playerStats.basesDestroyed, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            elseif achievement.id == "veteran" then
                achievement.progress = math.min(playerStats.timePlayed, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            elseif achievement.id == "master_builder" then
                achievement.progress = math.min(playerStats.structuresBuilt, achievement.maxProgress)
                shouldUnlock = achievement.progress >= achievement.maxProgress
            end
            
            -- Desbloqueia conquista se necessário
            if shouldUnlock then
                achievement.unlocked = true
                showAchievementUnlocked(achievement)
            end
        end
    end
end

-- Função para mostrar conquista desbloqueada
local function showAchievementUnlocked(achievement)
    -- Usa o sistema de notificações se disponível
    if _G.NotificationSystem then
        _G.NotificationSystem.achievementUnlocked(achievement.name)
    else
        print("🏆 Conquista desbloqueada: " .. achievement.name)
    end
end

-- Função para atualizar estatísticas do jogador
local function updatePlayerStats()
    if not player.Character then return end
    
    -- Atualiza tempo jogado
    playerStats.timePlayed = playerStats.timePlayed + (1/60)
    
    -- Verifica se tem base
    playerStats.basesOwned = 0
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerStats.basesOwned = 1
                break
            end
        end
    end
    
    -- Verifica recursos carregados
    local resourcesCarrying = 0
    if player.Character:FindFirstChild("HumanoidRootPart") then
        for _, child in ipairs(player.Character.HumanoidRootPart:GetChildren()) do
            if child.Name == "CarryingResource" then
                resourcesCarrying = resourcesCarrying + 1
            end
        end
    end
    
    -- Verifica conquistas
    checkAchievements()
end

-- Função para alternar menu de conquistas
function toggleAchievementMenu()
    if not achievementFrame then return end
    
    isAchievementMenuOpen = not isAchievementMenuOpen
    achievementFrame.Visible = isAchievementMenuOpen
    
    if isAchievementMenuOpen then
        -- Atualiza UI quando abre
        local statsScroll = achievementFrame:FindFirstChild("AchievementFrame"):FindFirstChild("Frame"):FindFirstChild("ScrollingFrame")
        local achievementsScroll = achievementFrame:FindFirstChild("AchievementFrame"):FindFirstChild("Frame"):FindFirstChild("ScrollingFrame")
        
        -- Encontra os ScrollingFrames corretos
        for _, child in ipairs(achievementFrame:GetChildren()) do
            if child:IsA("Frame") then
                for _, subChild in ipairs(child:GetChildren()) do
                    if subChild:IsA("ScrollingFrame") then
                        if child.Position.X.Scale < 0.5 then
                            updateStatsUI(subChild)
                        else
                            updateAchievementsUI(subChild)
                        end
                    end
                end
            end
        end
        
        print("🏆 Menu de conquistas aberto")
    else
        print("🏆 Menu de conquistas fechado")
    end
end

-- Inicialização
local function initializeAchievementSystem()
    local statsScroll, achievementsScroll = createAchievementUI()
    
    -- Loop de atualização
    RunService.Heartbeat:Connect(updatePlayerStats)
    
    -- Conecta tecla P para abrir conquistas
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.P then
            toggleAchievementMenu()
        end
    end)
    
    -- Atualiza UI inicial
    updateStatsUI(statsScroll)
    updateAchievementsUI(achievementsScroll)
end

-- Exporta funções para uso global
_G.AchievementSystem = {
    updateStat = function(statName, value)
        if playerStats[statName] then
            playerStats[statName] = playerStats[statName] + (value or 1)
        end
    end,
    setStat = function(statName, value)
        if playerStats[statName] then
            playerStats[statName] = value
        end
    end,
    getStats = function()
        return playerStats
    end
}

-- Inicializa quando o jogador spawna
if player.Character then
    initializeAchievementSystem()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeAchievementSystem()
end)

print("AchievementSystem carregado com sucesso!")
