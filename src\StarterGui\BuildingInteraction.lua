-- BuildingInteraction.lua
-- Gerencia a interação com construções (mover, deletar)

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local moveStructure = remoteEvents:WaitForChild("MoveStructure")
local deleteStructure = remoteEvents:WaitForChild("DeleteStructure")

-- Variáveis globais
local selectedBuilding = nil
local isDragging = false
local dragConnection = nil

-- Verifica se o jogador está na sua base
local function isPlayerInOwnBase()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    -- <PERSON><PERSON>ra por todas as bases no workspace
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            local barrier = base:FindFirstChild("Barrier")
            
            -- Verifica se o jogador é dono ou parceiro
            local isOwner = owner and owner.Value == player
            local isPartner = partner and partner.Value == player
            
            if (isOwner or isPartner) and barrier then
                -- Verifica se está dentro da barreira
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2
                
                if distance <= barrierRadius then
                    return true
                end
            end
        end
    end
    
    return false
end

-- Encontra a construção mais próxima do mouse
local function findNearestBuilding()
    local camera = workspace.CurrentCamera
    local unitRay = camera:ScreenPointToRay(mouse.X, mouse.Y)
    
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Whitelist
    
    -- Filtra apenas construções
    local buildings = {}
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj.Name:match("_Building$") and obj:FindFirstChild("BaseOwner") then
            table.insert(buildings, obj)
        end
    end
    
    raycastParams.FilterDescendantsInstances = buildings
    
    local raycastResult = workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)
    
    if raycastResult and raycastResult.Instance then
        return raycastResult.Instance
    end
    
    return nil
end

-- Verifica se o jogador pode interagir com a construção
local function canInteractWithBuilding(building)
    if not building or not building:FindFirstChild("BaseOwner") then
        return false
    end
    
    local base = building.BaseOwner.Value
    if not base then return false end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    
    local isOwner = owner and owner.Value == player
    local isPartner = partner and partner.Value == player
    
    return isOwner or isPartner
end

-- Inicia o modo de arrastar
local function startDragging(building)
    if not canInteractWithBuilding(building) then return end
    
    selectedBuilding = building
    isDragging = true
    
    -- Cria efeito visual
    local selectionBox = Instance.new("SelectionBox")
    selectionBox.Adornee = building
    selectionBox.Color3 = Color3.new(0, 1, 0)
    selectionBox.LineThickness = 0.3
    selectionBox.Transparency = 0.3
    selectionBox.Parent = building
    
    -- Conecta ao movimento do mouse
    dragConnection = mouse.Move:Connect(function()
        if isDragging and selectedBuilding then
            local camera = workspace.CurrentCamera
            local unitRay = camera:ScreenPointToRay(mouse.X, mouse.Y)
            
            -- Raycast para encontrar posição no chão
            local raycastParams = RaycastParams.new()
            raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
            raycastParams.FilterDescendantsInstances = {selectedBuilding}
            
            local raycastResult = workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)
            
            if raycastResult then
                local newPosition = raycastResult.Position + Vector3.new(0, selectedBuilding.Size.Y / 2, 0)
                selectedBuilding.Position = newPosition
            end
        end
    end)
    
    print("Arrastando construção. Clique novamente para confirmar posição ou Delete para remover.")
end

-- Para o modo de arrastar
local function stopDragging(confirm)
    if not isDragging or not selectedBuilding then return end
    
    if confirm then
        -- Confirma nova posição
        moveStructure:FireServer(selectedBuilding, selectedBuilding.Position)
        print("Posição da construção confirmada.")
    end
    
    -- Remove efeito visual
    local selectionBox = selectedBuilding:FindFirstChild("SelectionBox")
    if selectionBox then
        selectionBox:Destroy()
    end
    
    isDragging = false
    selectedBuilding = nil
    
    if dragConnection then
        dragConnection:Disconnect()
        dragConnection = nil
    end
end

-- Deleta a construção selecionada
local function deleteSelectedBuilding()
    if selectedBuilding and canInteractWithBuilding(selectedBuilding) then
        deleteStructure:FireServer(selectedBuilding)
        stopDragging(false)
        print("Construção removida.")
    end
end

-- Controle de entrada
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        if not isPlayerInOwnBase() then return end
        
        if isDragging then
            -- Confirma posição
            stopDragging(true)
        else
            -- Tenta selecionar construção
            local building = findNearestBuilding()
            if building and canInteractWithBuilding(building) then
                startDragging(building)
            end
        end
    elseif input.KeyCode == Enum.KeyCode.Delete then
        if isDragging then
            deleteSelectedBuilding()
        end
    elseif input.KeyCode == Enum.KeyCode.Escape then
        if isDragging then
            stopDragging(false)
        end
    end
end)

print("BuildingInteraction inicializado com sucesso!")