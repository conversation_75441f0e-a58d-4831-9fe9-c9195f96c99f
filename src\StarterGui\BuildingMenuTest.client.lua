-- BuildingMenuTest.client.lua
-- Script de teste para verificar se o menu de construção está funcionando

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local openBuildMenu = remoteEvents:WaitForChild("OpenBuildMenu")

print("🔨 BuildingMenuTest carregado!")

-- Função para verificar se o jogador está na sua base
local function isPlayerInOwnBase()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    -- Procura por bases próximas
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj.Name:match("Base_") then
            local owner = obj:FindFirstChild("Owner")
            if owner and owner.Value == player then
                local barrier = obj:FindFirstChild("Barrier")
                if barrier then
                    local distance = (playerPosition - barrier.Position).Magnitude
                    if distance <= barrier.Size.X / 2 then
                        return true, obj
                    end
                end
            end
        end
    end
    
    return false
end

-- Conecta tecla E
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.E then
        local inBase, base = isPlayerInOwnBase()
        print("🔨 Tecla E pressionada!")
        print("🏠 Está na base:", inBase)
        if base then
            print("🏠 Base:", base.Name)
        end
        
        if inBase then
            print("🔨 Enviando pedido para abrir menu...")
            openBuildMenu:FireServer()
        else
            print("❌ Você precisa estar dentro da sua base para construir!")
        end
    end
end)

print("✅ BuildingMenuTest inicializado - Pressione E para testar!")
