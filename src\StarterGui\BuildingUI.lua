-- BuildingUI.lua
-- Interface do usuário para o sistema de construção

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local openBuildMenu = remoteEvents:WaitForChild("OpenBuildMenu")
local closeBuildMenu = remoteEvents:WaitForChild("CloseBuildMenu")
local buildStructure = remoteEvents:WaitForChild("BuildStructure")
local moveStructure = remoteEvents:WaitForChild("MoveStructure")
local deleteStructure = remoteEvents:WaitForChild("DeleteStructure")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

-- Variáveis globais
local buildingGui = nil
local isMenuOpen = false
local selectedBuilding = nil
local buildingMaterials = 0
local baseSize = 0
local buildingTypes = {}

-- Cria a interface principal
local function createBuildingGUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BuildingGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Frame principal
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 400, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -200, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    mainFrame.BorderSizePixel = 2
    mainFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "CONSTRUÇÃO DE BASE"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Informações da base
    local infoFrame = Instance.new("Frame")
    infoFrame.Name = "InfoFrame"
    infoFrame.Size = UDim2.new(1, -20, 0, 60)
    infoFrame.Position = UDim2.new(0, 10, 0, 50)
    infoFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    infoFrame.BorderSizePixel = 1
    infoFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    infoFrame.Parent = mainFrame
    
    local materialsLabel = Instance.new("TextLabel")
    materialsLabel.Name = "MaterialsLabel"
    materialsLabel.Size = UDim2.new(1, 0, 0.5, 0)
    materialsLabel.Position = UDim2.new(0, 0, 0, 0)
    materialsLabel.BackgroundTransparency = 1
    materialsLabel.Text = "Materiais: 0"
    materialsLabel.TextColor3 = Color3.new(1, 1, 0)
    materialsLabel.TextScaled = true
    materialsLabel.Font = Enum.Font.SourceSans
    materialsLabel.Parent = infoFrame
    
    local sizeLabel = Instance.new("TextLabel")
    sizeLabel.Name = "SizeLabel"
    sizeLabel.Size = UDim2.new(1, 0, 0.5, 0)
    sizeLabel.Position = UDim2.new(0, 0, 0.5, 0)
    sizeLabel.BackgroundTransparency = 1
    sizeLabel.Text = "Tamanho da Base: 100"
    sizeLabel.TextColor3 = Color3.new(0, 1, 1)
    sizeLabel.TextScaled = true
    sizeLabel.Font = Enum.Font.SourceSans
    sizeLabel.Parent = infoFrame
    
    -- ScrollFrame para construções
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "BuildingsScrollFrame"
    scrollFrame.Size = UDim2.new(1, -20, 1, -170)
    scrollFrame.Position = UDim2.new(0, 10, 0, 120)
    scrollFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    scrollFrame.BorderSizePixel = 1
    scrollFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    scrollFrame.ScrollBarThickness = 10
    scrollFrame.Parent = mainFrame
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 80, 0, 30)
    closeButton.Position = UDim2.new(1, -90, 1, -40)
    closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "FECHAR"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    closeButton.MouseButton1Click:Connect(function()
        closeBuildingMenu()
    end)
    
    return screenGui
end

-- Cria botão de construção
local function createBuildingButton(buildingType, index)
    local button = Instance.new("TextButton")
    button.Name = buildingType.name .. "Button"
    button.Size = UDim2.new(1, -10, 0, 80)
    button.Position = UDim2.new(0, 5, 0, (index - 1) * 85)
    button.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    button.BorderSizePixel = 1
    button.BorderColor3 = Color3.new(0.4, 0.4, 0.4)
    button.Text = ""
    
    -- Nome da construção
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(0.7, 0, 0.4, 0)
    nameLabel.Position = UDim2.new(0, 5, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = buildingType.displayName
    nameLabel.TextColor3 = Color3.new(1, 1, 1)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = button
    
    -- Custo
    local costLabel = Instance.new("TextLabel")
    costLabel.Size = UDim2.new(0.3, 0, 0.4, 0)
    costLabel.Position = UDim2.new(0.7, 0, 0, 0)
    costLabel.BackgroundTransparency = 1
    costLabel.Text = "Custo: " .. buildingType.cost
    costLabel.TextColor3 = Color3.new(1, 1, 0)
    costLabel.TextScaled = true
    costLabel.Font = Enum.Font.SourceSans
    costLabel.TextXAlignment = Enum.TextXAlignment.Right
    costLabel.Parent = button
    
    -- Descrição
    local descLabel = Instance.new("TextLabel")
    descLabel.Size = UDim2.new(1, -10, 0.6, 0)
    descLabel.Position = UDim2.new(0, 5, 0.4, 0)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = buildingType.description
    descLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.SourceSans
    descLabel.TextXAlignment = Enum.TextXAlignment.Left
    descLabel.TextWrapped = true
    descLabel.Parent = button
    
    -- Atualiza cor baseado nos materiais disponíveis
    local function updateButtonState()
        if buildingMaterials >= buildingType.cost then
            button.BackgroundColor3 = Color3.new(0.2, 0.4, 0.2)
            costLabel.TextColor3 = Color3.new(0, 1, 0)
        else
            button.BackgroundColor3 = Color3.new(0.4, 0.2, 0.2)
            costLabel.TextColor3 = Color3.new(1, 0, 0)
        end
    end
    
    updateButtonState()
    
    -- Evento de clique
    button.MouseButton1Click:Connect(function()
        if buildingMaterials >= buildingType.cost then
            startBuildingPlacement(buildingType)
        end
    end)
    
    return button, updateButtonState
end

-- Atualiza a lista de construções
local function updateBuildingsList()
    if not buildingGui then return end
    
    local scrollFrame = buildingGui.MainFrame.BuildingsScrollFrame
    
    -- Remove botões existentes
    for _, child in ipairs(scrollFrame:GetChildren()) do
        if child:IsA("TextButton") then
            child:Destroy()
        end
    end
    
    -- Cria novos botões
    local updateFunctions = {}
    for i, buildingType in ipairs(buildingTypes) do
        local button, updateFunc = createBuildingButton(buildingType, i)
        button.Parent = scrollFrame
        table.insert(updateFunctions, updateFunc)
    end
    
    -- Atualiza tamanho do conteúdo
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, #buildingTypes * 85)
    
    -- Retorna funções de atualização
    return updateFunctions
end

-- Inicia o modo de colocação de construção
local function startBuildingPlacement(buildingType)
    closeBuildingMenu()
    
    -- Aqui você implementaria o sistema de preview e colocação
    -- Por simplicidade, vamos colocar na posição do jogador
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        local position = player.Character.HumanoidRootPart.Position + Vector3.new(0, 0, 10)
        buildStructure:FireServer(buildingType.name, position)
    end
end

-- Abre o menu de construção
local function openBuildingMenu()
    if isMenuOpen then return end

    print("🔨 Tentando abrir menu de construção...")
    openBuildMenu:FireServer()
end

-- Fecha o menu de construção
function closeBuildingMenu()
    if not isMenuOpen then return end
    
    isMenuOpen = false
    if buildingGui then
        buildingGui.MainFrame.Visible = false
    end
    
    closeBuildMenu:FireServer()
end

-- Atualiza informações da base
updateBaseInfo.OnClientEvent:Connect(function(baseInfo)
    buildingMaterials = baseInfo.buildingMaterials
    baseSize = baseInfo.baseSize
    buildingTypes = baseInfo.buildingTypes
    
    if not buildingGui then
        buildingGui = createBuildingGUI()
    end
    
    -- Atualiza labels
    local mainFrame = buildingGui.MainFrame
    mainFrame.InfoFrame.MaterialsLabel.Text = "Materiais: " .. buildingMaterials
    mainFrame.InfoFrame.SizeLabel.Text = "Tamanho da Base: " .. baseSize
    
    -- Atualiza lista de construções
    local updateFunctions = updateBuildingsList()
    
    -- Atualiza estado dos botões
    for _, updateFunc in ipairs(updateFunctions or {}) do
        updateFunc()
    end
    
    -- Mostra o menu
    isMenuOpen = true
    mainFrame.Visible = true
end)

-- Controle de entrada
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.E then
        print("Tecla E pressionada. Menu aberto:", isMenuOpen)
        if not isMenuOpen then
            openBuildingMenu()
        else
            closeBuildingMenu()
        end
    end
end)

print("BuildingUI inicializado com sucesso!")