-- BuildingUI.lua
-- Interface de construção visível apenas dentro da própria barreira

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local requestBuild = remoteEvents:WaitForChild("RequestBuild")
local buildResponse = remoteEvents:WaitForChild("BuildResponse")

-- Variáveis da UI
local screenGui = nil
local buildingFrame = nil
local isInOwnBarrier = false
local selectedBuildingType = nil
local previewPart = nil

-- Tipos de construções
local BUILDING_TYPES = {
    {name = "Wall", displayName = "Muro de Pedra", cost = 25, color = Color3.new(0.5, 0.5, 0.5)},
    {name = "Tower", displayName = "Torre de Vigia", cost = 50, color = Color3.new(0.6, 0.4, 0.2)},
    {name = "Generator", displayName = "Gerador de Barreira", cost = 75, color = Color3.new(0, 0.8, 1)},
    {name = "Depot", displayName = "Depósito de Recursos", cost = 40, color = Color3.new(1, 1, 0)}
}

-- Função para verificar se está na própria barreira
local function checkIfInOwnBarrier()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    -- Verifica todas as bases
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            -- Verifica se é da equipe do jogador
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                local barrier = base:FindFirstChild("Barrier")
                if barrier then
                    local distance = (playerPosition - barrier.Position).Magnitude
                    local barrierRadius = barrier.Size.X / 2
                    
                    return distance <= barrierRadius
                end
            end
        end
    end
    
    return false
end

-- Função para criar a interface de construção
local function createBuildingUI()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("BuildingUI")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BuildingUI"
    screenGui.Parent = playerGui
    
    -- Frame principal de construção
    buildingFrame = Instance.new("Frame")
    buildingFrame.Name = "BuildingFrame"
    buildingFrame.Size = UDim2.new(0, 250, 0, 300)
    buildingFrame.Position = UDim2.new(1, -260, 0.5, -150)
    buildingFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    buildingFrame.BackgroundTransparency = 0.3
    buildingFrame.BorderSizePixel = 2
    buildingFrame.BorderColor3 = Color3.new(1, 1, 1)
    buildingFrame.Visible = false
    buildingFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 30)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🔨 CONSTRUÇÃO"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = buildingFrame
    
    -- Instruções
    local instructionLabel = Instance.new("TextLabel")
    instructionLabel.Size = UDim2.new(1, 0, 0, 40)
    instructionLabel.Position = UDim2.new(0, 0, 0, 30)
    instructionLabel.BackgroundTransparency = 1
    instructionLabel.Text = "Pressione 'B' para abrir/fechar\nSelecione uma estrutura e clique para posicionar"
    instructionLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    instructionLabel.TextScaled = true
    instructionLabel.TextWrapped = true
    instructionLabel.Font = Enum.Font.SourceSans
    instructionLabel.Parent = buildingFrame
    
    -- Botões de construção
    local yPosition = 75
    for i, buildingType in ipairs(BUILDING_TYPES) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, -10, 0, 40)
        button.Position = UDim2.new(0, 5, 0, yPosition)
        button.BackgroundColor3 = buildingType.color
        button.BorderSizePixel = 2
        button.BorderColor3 = Color3.new(1, 1, 1)
        button.Text = buildingType.displayName .. "\nCusto: " .. buildingType.cost .. " materiais"
        button.TextColor3 = Color3.new(1, 1, 1)
        button.TextScaled = true
        button.TextWrapped = true
        button.Font = Enum.Font.SourceSansBold
        button.Parent = buildingFrame
        
        -- Conecta evento de clique
        button.MouseButton1Click:Connect(function()
            selectedBuildingType = buildingType.name
            createPreview(buildingType)
            buildingFrame.Visible = false
        end)
        
        yPosition = yPosition + 45
    end
    
    -- Botão de fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(1, -10, 0, 30)
    closeButton.Position = UDim2.new(0, 5, 1, -35)
    closeButton.BackgroundColor3 = Color3.new(0.7, 0, 0)
    closeButton.BorderSizePixel = 1
    closeButton.BorderColor3 = Color3.new(1, 1, 1)
    closeButton.Text = "FECHAR"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = buildingFrame
    
    closeButton.MouseButton1Click:Connect(function()
        buildingFrame.Visible = false
        clearPreview()
    end)
end

-- Função para criar preview da construção
local function createPreview(buildingType)
    clearPreview()
    
    -- Tamanhos baseados no tipo
    local sizes = {
        Wall = Vector3.new(8, 6, 2),
        Tower = Vector3.new(4, 12, 4),
        Generator = Vector3.new(6, 4, 6),
        Depot = Vector3.new(6, 6, 6)
    }
    
    previewPart = Instance.new("Part")
    previewPart.Name = "BuildingPreview"
    previewPart.Size = sizes[buildingType.name] or Vector3.new(4, 4, 4)
    previewPart.BrickColor = BrickColor.new(buildingType.color)
    previewPart.Material = Enum.Material.ForceField
    previewPart.Transparency = 0.5
    previewPart.CanCollide = false
    previewPart.Anchored = true
    previewPart.Parent = workspace
end

-- Função para limpar preview
local function clearPreview()
    if previewPart then
        previewPart:Destroy()
        previewPart = nil
    end
    selectedBuildingType = nil
end

-- Função para atualizar posição do preview
local function updatePreview()
    if previewPart and mouse.Hit then
        local position = mouse.Hit.Position
        position = Vector3.new(position.X, position.Y + previewPart.Size.Y / 2, position.Z)
        previewPart.Position = position
    end
end

-- Função para confirmar construção
local function confirmBuild()
    if selectedBuildingType and previewPart then
        local position = previewPart.Position
        requestBuild:FireServer(selectedBuildingType, position)
        clearPreview()
    end
end

-- Inicializa a UI
local function initializeBuildingUI()
    createBuildingUI()

    -- Configura clique na bandeira
    setupFlagClick()

    -- Loop para verificar se está na própria barreira e reconfigurar bandeira
    spawn(function()
        while true do
            wait(2) -- Verifica a cada 2 segundos
            local wasInBarrier = isInOwnBarrier
            isInOwnBarrier = checkIfInOwnBarrier()

            -- Se saiu da barreira, fecha a UI
            if wasInBarrier and not isInOwnBarrier then
                buildingFrame.Visible = false
                clearPreview()
            end

            -- Reconfigura bandeira periodicamente (caso o jogador tenha reivindicado uma base)
            setupFlagClick()
        end
    end)

    -- Loop para atualizar preview
    RunService.Heartbeat:Connect(updatePreview)
end

-- Função para abrir menu de construção
local function openBuildingMenu()
    if isInOwnBarrier then
        buildingFrame.Visible = not buildingFrame.Visible
        if not buildingFrame.Visible then
            clearPreview()
        end
    end
end

-- Função para configurar clique na bandeira
local function setupFlagClick()
    -- Encontra a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")

            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                local coreTower = base:FindFirstChild("CoreTower")
                if coreTower then
                    local flag = coreTower:FindFirstChild("Flag")
                    if flag then
                        -- Adiciona ClickDetector na bandeira
                        local clickDetector = flag:FindFirstChild("ClickDetector")
                        if not clickDetector then
                            clickDetector = Instance.new("ClickDetector")
                            clickDetector.MaxActivationDistance = 20
                            clickDetector.Parent = flag
                        end

                        -- Conecta evento de clique
                        clickDetector.MouseClick:Connect(function(playerWhoClicked)
                            if playerWhoClicked == player then
                                openBuildingMenu()
                            end
                        end)

                        -- Adiciona texto informativo na bandeira
                        local surfaceGui = flag:FindFirstChild("SurfaceGui")
                        if not surfaceGui then
                            surfaceGui = Instance.new("SurfaceGui")
                            surfaceGui.Face = Enum.NormalId.Front
                            surfaceGui.Parent = flag

                            local textLabel = Instance.new("TextLabel")
                            textLabel.Size = UDim2.new(1, 0, 1, 0)
                            textLabel.BackgroundTransparency = 1
                            textLabel.Text = "🏗️ CLIQUE PARA\nCONSTRUIR"
                            textLabel.TextColor3 = Color3.new(1, 1, 1)
                            textLabel.TextScaled = true
                            textLabel.Font = Enum.Font.SourceSansBold
                            textLabel.TextStrokeTransparency = 0
                            textLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
                            textLabel.Parent = surfaceGui
                        end
                    end
                end
                break
            end
        end
    end
end

-- Conecta eventos de input
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.B then
        -- Tecla B para abrir/fechar menu de construção
        openBuildingMenu()
    end
end)

-- Conecta clique do mouse para confirmar construção
mouse.Button1Down:Connect(function()
    if selectedBuildingType and previewPart then
        confirmBuild()
    end
end)

-- Conecta resposta de construção
buildResponse.OnClientEvent:Connect(function(success, message)
    -- Cria notificação
    local playerGui = player:WaitForChild("PlayerGui")
    
    local notification = Instance.new("ScreenGui")
    notification.Name = "BuildNotification"
    notification.Parent = playerGui
    
    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(0, 300, 0, 60)
    frame.Position = UDim2.new(0.5, -150, 0, 100)
    frame.BackgroundColor3 = success and Color3.new(0, 0.7, 0) or Color3.new(0.7, 0, 0)
    frame.BorderSizePixel = 2
    frame.BorderColor3 = Color3.new(1, 1, 1)
    frame.Parent = notification
    
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = message
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextScaled = true
    label.TextWrapped = true
    label.Font = Enum.Font.SourceSansBold
    label.Parent = frame
    
    -- Remove notificação após 3 segundos
    spawn(function()
        wait(3)
        notification:Destroy()
    end)
end)

-- Inicializa quando o jogador spawna
if player.Character then
    initializeBuildingUI()
end

player.CharacterAdded:Connect(function()
    wait(1)
    initializeBuildingUI()
end)

print("BuildingUI carregado com sucesso!")