-- CompletePlayerMenu.lua
-- Menu completo de dados do jogador com TAB para abrir/fechar

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Aguarda o jogador carregar
if not player.Character then
    player.CharacterAdded:Wait()
end

-- Variáveis da UI
local screenGui = nil
local mainFrame = nil
local isMenuOpen = false

-- Dados do jogador
local playerData = {
    health = 100,
    maxHealth = 100,
    baseName = "Nenhuma",
    baseSize = 0,
    materials = 0,
    resourcesCollected = 0,
    enemiesKilled = 0,
    timePlayed = 0,
    inventory = {
        "🔫 CombatGun",
        "🔨 CollectorGun"
    },
    achievements = {
        "🎮 Bem-vindo à Arena!",
        "🏠 Primeira Base (Pendente)",
        "⚔️ Primeiro Combate (Pendente)",
        "💎 Primeiro Recurso (Pendente)",
        "🤝 Primeira Dupla (Pendente)"
    },
    stats = {
        basesDestroyed = 0,
        resourcesDeposited = 0,
        timeInBattle = 0,
        partnersInvited = 0,
        deathCount = 0,
        killCount = 0
    }
}

-- Função para criar o menu completo
local function createCompleteMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove menu existente
    local existingMenu = playerGui:FindFirstChild("CompletePlayerMenu")
    if existingMenu then existingMenu:Destroy() end
    
    -- Cria ScreenGui
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CompletePlayerMenu"
    screenGui.Parent = playerGui
    
    -- Frame principal (inicialmente invisível)
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 600, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -300, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    mainFrame.BackgroundTransparency = 0.1
    mainFrame.BorderSizePixel = 3
    mainFrame.BorderColor3 = Color3.new(0, 1, 1)
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 50)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.BackgroundTransparency = 0
    titleLabel.Text = "🎮 DADOS DO JOGADOR - " .. player.Name
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    -- Scroll Frame para conteúdo
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 1, -70)
    scrollFrame.Position = UDim2.new(0, 10, 0, 60)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 10
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 800)
    scrollFrame.Parent = mainFrame
    
    -- Seção Status
    local statusFrame = Instance.new("Frame")
    statusFrame.Size = UDim2.new(1, -20, 0, 120)
    statusFrame.Position = UDim2.new(0, 10, 0, 10)
    statusFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    statusFrame.BorderSizePixel = 2
    statusFrame.BorderColor3 = Color3.new(0, 1, 0)
    statusFrame.Parent = scrollFrame
    
    local statusTitle = Instance.new("TextLabel")
    statusTitle.Size = UDim2.new(1, 0, 0, 25)
    statusTitle.Position = UDim2.new(0, 0, 0, 0)
    statusTitle.BackgroundColor3 = Color3.new(0, 0.8, 0)
    statusTitle.Text = "📊 STATUS ATUAL"
    statusTitle.TextColor3 = Color3.new(1, 1, 1)
    statusTitle.TextScaled = true
    statusTitle.Font = Enum.Font.SourceSansBold
    statusTitle.Parent = statusFrame
    
    local statusText = Instance.new("TextLabel")
    statusText.Name = "StatusText"
    statusText.Size = UDim2.new(1, -10, 1, -30)
    statusText.Position = UDim2.new(0, 5, 0, 30)
    statusText.BackgroundTransparency = 1
    statusText.Text = "❤️ Vida: 100/100\n🏠 Base: Nenhuma\n⏰ Tempo: 0:00\n💎 Recursos: 0"
    statusText.TextColor3 = Color3.new(1, 1, 1)
    statusText.TextScaled = true
    statusText.TextWrapped = true
    statusText.TextXAlignment = Enum.TextXAlignment.Left
    statusText.Font = Enum.Font.SourceSans
    statusText.Parent = statusFrame
    
    -- Seção Inventário
    local inventoryFrame = Instance.new("Frame")
    inventoryFrame.Size = UDim2.new(1, -20, 0, 120)
    inventoryFrame.Position = UDim2.new(0, 10, 0, 140)
    inventoryFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    inventoryFrame.BorderSizePixel = 2
    inventoryFrame.BorderColor3 = Color3.new(0, 0, 1)
    inventoryFrame.Parent = scrollFrame
    
    local inventoryTitle = Instance.new("TextLabel")
    inventoryTitle.Size = UDim2.new(1, 0, 0, 25)
    inventoryTitle.Position = UDim2.new(0, 0, 0, 0)
    inventoryTitle.BackgroundColor3 = Color3.new(0, 0, 0.8)
    inventoryTitle.Text = "🎒 INVENTÁRIO"
    inventoryTitle.TextColor3 = Color3.new(1, 1, 1)
    inventoryTitle.TextScaled = true
    inventoryTitle.Font = Enum.Font.SourceSansBold
    inventoryTitle.Parent = inventoryFrame
    
    local inventoryText = Instance.new("TextLabel")
    inventoryText.Name = "InventoryText"
    inventoryText.Size = UDim2.new(1, -10, 1, -30)
    inventoryText.Position = UDim2.new(0, 5, 0, 30)
    inventoryText.BackgroundTransparency = 1
    inventoryText.Text = "🔫 CombatGun\n🔨 CollectorGun\n💎 Recursos carregados: 0"
    inventoryText.TextColor3 = Color3.new(1, 1, 1)
    inventoryText.TextScaled = true
    inventoryText.TextWrapped = true
    inventoryText.TextXAlignment = Enum.TextXAlignment.Left
    inventoryText.Font = Enum.Font.SourceSans
    inventoryText.Parent = inventoryFrame
    
    -- Seção Conquistas
    local achievementsFrame = Instance.new("Frame")
    achievementsFrame.Size = UDim2.new(1, -20, 0, 150)
    achievementsFrame.Position = UDim2.new(0, 10, 0, 270)
    achievementsFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    achievementsFrame.BorderSizePixel = 2
    achievementsFrame.BorderColor3 = Color3.new(1, 1, 0)
    achievementsFrame.Parent = scrollFrame
    
    local achievementsTitle = Instance.new("TextLabel")
    achievementsTitle.Size = UDim2.new(1, 0, 0, 25)
    achievementsTitle.Position = UDim2.new(0, 0, 0, 0)
    achievementsTitle.BackgroundColor3 = Color3.new(0.8, 0.8, 0)
    achievementsTitle.Text = "🏆 CONQUISTAS"
    achievementsTitle.TextColor3 = Color3.new(0, 0, 0)
    achievementsTitle.TextScaled = true
    achievementsTitle.Font = Enum.Font.SourceSansBold
    achievementsTitle.Parent = achievementsFrame
    
    local achievementsText = Instance.new("TextLabel")
    achievementsText.Name = "AchievementsText"
    achievementsText.Size = UDim2.new(1, -10, 1, -30)
    achievementsText.Position = UDim2.new(0, 5, 0, 30)
    achievementsText.BackgroundTransparency = 1
    achievementsText.Text = "🎮 Bem-vindo à Arena!\n🏠 Primeira Base (Pendente)\n⚔️ Primeiro Combate (Pendente)\n💎 Primeiro Recurso (Pendente)\n🤝 Primeira Dupla (Pendente)"
    achievementsText.TextColor3 = Color3.new(1, 1, 1)
    achievementsText.TextScaled = true
    achievementsText.TextWrapped = true
    achievementsText.TextXAlignment = Enum.TextXAlignment.Left
    achievementsText.Font = Enum.Font.SourceSans
    achievementsText.Parent = achievementsFrame
    
    -- Seção Estatísticas
    local statsFrame = Instance.new("Frame")
    statsFrame.Size = UDim2.new(1, -20, 0, 150)
    statsFrame.Position = UDim2.new(0, 10, 0, 430)
    statsFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    statsFrame.BorderSizePixel = 2
    statsFrame.BorderColor3 = Color3.new(1, 0, 1)
    statsFrame.Parent = scrollFrame
    
    local statsTitle = Instance.new("TextLabel")
    statsTitle.Size = UDim2.new(1, 0, 0, 25)
    statsTitle.Position = UDim2.new(0, 0, 0, 0)
    statsTitle.BackgroundColor3 = Color3.new(0.8, 0, 0.8)
    statsTitle.Text = "📈 ESTATÍSTICAS"
    statsTitle.TextColor3 = Color3.new(1, 1, 1)
    statsTitle.TextScaled = true
    statsTitle.Font = Enum.Font.SourceSansBold
    statsTitle.Parent = statsFrame
    
    local statsText = Instance.new("TextLabel")
    statsText.Name = "StatsText"
    statsText.Size = UDim2.new(1, -10, 1, -30)
    statsText.Position = UDim2.new(0, 5, 0, 30)
    statsText.BackgroundTransparency = 1
    statsText.Text = "💀 Eliminações: 0\n☠️ Mortes: 0\n🏠 Bases Destruídas: 0\n📦 Recursos Depositados: 0\n⚔️ Tempo em Combate: 0s\n🤝 Convites Enviados: 0"
    statsText.TextColor3 = Color3.new(1, 1, 1)
    statsText.TextScaled = true
    statsText.TextWrapped = true
    statsText.TextXAlignment = Enum.TextXAlignment.Left
    statsText.Font = Enum.Font.SourceSans
    statsText.Parent = statsFrame
    
    -- Conecta botão fechar
    closeButton.MouseButton1Click:Connect(function()
        toggleMenu()
    end)
    
    return statusText, inventoryText, achievementsText, statsText
end

-- Função para alternar visibilidade do menu
function toggleMenu()
    if not mainFrame then return end

    isMenuOpen = not isMenuOpen
    mainFrame.Visible = isMenuOpen

    if isMenuOpen then
        print("📋 Menu do jogador aberto - Pressione TAB para fechar")
    else
        print("📋 Menu do jogador fechado")
    end
end

-- Função para atualizar dados do jogador
local function updatePlayerData()
    if not player.Character then return end

    -- Atualiza vida
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        playerData.health = math.floor(humanoid.Health)
        playerData.maxHealth = math.floor(humanoid.MaxHealth)
    end

    -- Atualiza base
    playerData.baseName = "Nenhuma"
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")

            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerData.baseName = base.Name
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local materialsValue = base:FindFirstChild("BuildingMaterials")

                if baseSizeValue then playerData.baseSize = math.floor(baseSizeValue.Value) end
                if materialsValue then playerData.materials = materialsValue.Value end
                break
            end
        end
    end

    -- Atualiza tempo jogado
    playerData.timePlayed = playerData.timePlayed + 1/60 -- Aproximadamente 1 segundo por 60 frames

    -- Verifica ferramentas no inventário
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    local hasCombatGun = false
    local hasCollectorGun = false
    local carryingResource = 0

    if backpack then
        hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
    end

    if character then
        hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil

        -- Verifica se está carregando recursos
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if humanoidRootPart then
            for _, child in ipairs(humanoidRootPart:GetChildren()) do
                if child.Name == "CarryingResource" then
                    carryingResource = carryingResource + 1
                end
            end
        end
    end

    -- Atualiza inventário
    playerData.inventory = {}
    if hasCombatGun then table.insert(playerData.inventory, "🔫 CombatGun") end
    if hasCollectorGun then table.insert(playerData.inventory, "🔨 CollectorGun") end
    if carryingResource > 0 then table.insert(playerData.inventory, "💎 Recursos: " .. carryingResource) end
end

-- Função para atualizar UI
local function updateUI(statusText, inventoryText, achievementsText, statsText)
    if not statusText or not statusText.Parent then return end

    -- Atualiza status
    local minutes = math.floor(playerData.timePlayed / 60)
    local seconds = math.floor(playerData.timePlayed % 60)
    statusText.Text = string.format(
        "❤️ Vida: %d/%d\n🏠 Base: %s\n⏰ Tempo: %d:%02d\n💎 Recursos: %d\n🔧 Materiais: %d",
        playerData.health, playerData.maxHealth,
        playerData.baseName,
        minutes, seconds,
        playerData.resourcesCollected,
        playerData.materials
    )

    -- Atualiza inventário
    local inventoryStr = ""
    for i, item in ipairs(playerData.inventory) do
        inventoryStr = inventoryStr .. item
        if i < #playerData.inventory then
            inventoryStr = inventoryStr .. "\n"
        end
    end
    if inventoryStr == "" then inventoryStr = "Inventário vazio" end
    inventoryText.Text = inventoryStr

    -- Atualiza conquistas
    local achievementsStr = ""
    for i, achievement in ipairs(playerData.achievements) do
        achievementsStr = achievementsStr .. achievement
        if i < #playerData.achievements then
            achievementsStr = achievementsStr .. "\n"
        end
    end
    achievementsText.Text = achievementsStr

    -- Atualiza estatísticas
    statsText.Text = string.format(
        "💀 Eliminações: %d\n☠️ Mortes: %d\n🏠 Bases Destruídas: %d\n📦 Recursos Depositados: %d\n⚔️ Tempo em Combate: %ds\n🤝 Convites Enviados: %d",
        playerData.stats.killCount,
        playerData.stats.deathCount,
        playerData.stats.basesDestroyed,
        playerData.stats.resourcesDeposited,
        math.floor(playerData.stats.timeInBattle),
        playerData.stats.partnersInvited
    )
end

-- Inicialização
local function initializeMenu()
    local statusText, inventoryText, achievementsText, statsText = createCompleteMenu()

    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        if not player.Character then
            return
        end
        updatePlayerData()
        updateUI(statusText, inventoryText, achievementsText, statsText)
    end)

    -- Conecta tecla TAB
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.Tab then
            toggleMenu()
        end
    end)

    print("✅ Menu completo do jogador carregado! Pressione TAB para abrir/fechar")
end

-- Inicializa quando o jogador spawna
local function onCharacterAdded()
    task.wait(2)
    initializeMenu()
end

-- Conecta eventos
if player.Character then
    onCharacterAdded()
end

player.CharacterAdded:Connect(onCharacterAdded)
