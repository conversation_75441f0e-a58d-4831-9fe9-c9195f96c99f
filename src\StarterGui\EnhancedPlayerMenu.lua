-- EnhancedPlayerMenu.lua
-- Menu completo e melhorado do jogador com TAB para abrir/fechar

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Aguarda o jogador carregar
if not player.Character then
    player.CharacterAdded:Wait()
end

-- Variáveis da UI
local screenGui = nil
local mainFrame = nil
local isMenuOpen = false

-- Dados do jogador
local playerData = {
    health = 100,
    maxHealth = 100,
    baseName = "Nenhuma",
    baseSize = 0,
    maxBaseSize = 500,
    materials = 0,
    resourcesCollected = 0,
    enemiesKilled = 0,
    timePlayed = 0,
    deathCount = 0,
    basesDestroyed = 0,
    resourcesDeposited = 0,
    partnersInvited = 0,
    hasPartner = false,
    partnerName = "",
    carryingResources = 0,
    achievements = {
        welcomeGame = true,
        firstBase = false,
        firstCombat = false,
        firstResource = false,
        firstPartner = false,
        firstKill = false,
        resourceCollector = false,
        baseDestroyer = false
    }
}

-- Função para criar o menu completo
local function createEnhancedMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove menu existente
    local existingMenu = playerGui:FindFirstChild("EnhancedPlayerMenu")
    if existingMenu then existingMenu:Destroy() end
    
    -- Cria ScreenGui
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "EnhancedPlayerMenu"
    screenGui.Parent = playerGui
    
    -- Frame principal (inicialmente invisível)
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 700, 0, 600)
    mainFrame.Position = UDim2.new(0.5, -350, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    mainFrame.BackgroundTransparency = 0.1
    mainFrame.BorderSizePixel = 3
    mainFrame.BorderColor3 = Color3.new(0, 1, 1)
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Efeito de borda brilhante
    local borderGlow = Instance.new("UIStroke")
    borderGlow.Color = Color3.new(0, 1, 1)
    borderGlow.Thickness = 2
    borderGlow.Transparency = 0.3
    borderGlow.Parent = mainFrame
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 60)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.BackgroundTransparency = 0
    titleLabel.Text = "🎮 PERFIL DO JOGADOR - " .. player.Name
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Gradiente no título
    local titleGradient = Instance.new("UIGradient")
    titleGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.new(0, 0.8, 0.8)),
        ColorSequenceKeypoint.new(1, Color3.new(0, 0.6, 0.6))
    }
    titleGradient.Parent = titleLabel
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -55, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    -- Scroll Frame para conteúdo
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 1, -80)
    scrollFrame.Position = UDim2.new(0, 10, 0, 70)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 12
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 1000)
    scrollFrame.Parent = mainFrame
    
    -- === SEÇÃO STATUS VITAL ===
    local statusFrame = Instance.new("Frame")
    statusFrame.Size = UDim2.new(1, -20, 0, 140)
    statusFrame.Position = UDim2.new(0, 10, 0, 10)
    statusFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    statusFrame.BorderSizePixel = 2
    statusFrame.BorderColor3 = Color3.new(0, 1, 0)
    statusFrame.Parent = scrollFrame
    
    local statusTitle = Instance.new("TextLabel")
    statusTitle.Size = UDim2.new(1, 0, 0, 30)
    statusTitle.Position = UDim2.new(0, 0, 0, 0)
    statusTitle.BackgroundColor3 = Color3.new(0, 0.8, 0)
    statusTitle.Text = "💚 STATUS VITAL"
    statusTitle.TextColor3 = Color3.new(1, 1, 1)
    statusTitle.TextScaled = true
    statusTitle.Font = Enum.Font.SourceSansBold
    statusTitle.Parent = statusFrame
    
    local statusText = Instance.new("TextLabel")
    statusText.Name = "StatusText"
    statusText.Size = UDim2.new(1, -10, 1, -35)
    statusText.Position = UDim2.new(0, 5, 0, 35)
    statusText.BackgroundTransparency = 1
    statusText.Text = ""
    statusText.TextColor3 = Color3.new(1, 1, 1)
    statusText.TextSize = 16
    statusText.TextWrapped = true
    statusText.TextXAlignment = Enum.TextXAlignment.Left
    statusText.TextYAlignment = Enum.TextYAlignment.Top
    statusText.Font = Enum.Font.SourceSans
    statusText.Parent = statusFrame
    
    -- === SEÇÃO BASE E RECURSOS ===
    local baseFrame = Instance.new("Frame")
    baseFrame.Size = UDim2.new(1, -20, 0, 140)
    baseFrame.Position = UDim2.new(0, 10, 0, 160)
    baseFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    baseFrame.BorderSizePixel = 2
    baseFrame.BorderColor3 = Color3.new(1, 0.8, 0)
    baseFrame.Parent = scrollFrame
    
    local baseTitle = Instance.new("TextLabel")
    baseTitle.Size = UDim2.new(1, 0, 0, 30)
    baseTitle.Position = UDim2.new(0, 0, 0, 0)
    baseTitle.BackgroundColor3 = Color3.new(1, 0.8, 0)
    baseTitle.Text = "🏠 BASE E RECURSOS"
    baseTitle.TextColor3 = Color3.new(0, 0, 0)
    baseTitle.TextScaled = true
    baseTitle.Font = Enum.Font.SourceSansBold
    baseTitle.Parent = baseFrame
    
    local baseText = Instance.new("TextLabel")
    baseText.Name = "BaseText"
    baseText.Size = UDim2.new(1, -10, 1, -35)
    baseText.Position = UDim2.new(0, 5, 0, 35)
    baseText.BackgroundTransparency = 1
    baseText.Text = ""
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextSize = 16
    baseText.TextWrapped = true
    baseText.TextXAlignment = Enum.TextXAlignment.Left
    baseText.TextYAlignment = Enum.TextYAlignment.Top
    baseText.Font = Enum.Font.SourceSans
    baseText.Parent = baseFrame
    
    -- === SEÇÃO INVENTÁRIO ===
    local inventoryFrame = Instance.new("Frame")
    inventoryFrame.Size = UDim2.new(1, -20, 0, 140)
    inventoryFrame.Position = UDim2.new(0, 10, 0, 310)
    inventoryFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    inventoryFrame.BorderSizePixel = 2
    inventoryFrame.BorderColor3 = Color3.new(0, 0, 1)
    inventoryFrame.Parent = scrollFrame
    
    local inventoryTitle = Instance.new("TextLabel")
    inventoryTitle.Size = UDim2.new(1, 0, 0, 30)
    inventoryTitle.Position = UDim2.new(0, 0, 0, 0)
    inventoryTitle.BackgroundColor3 = Color3.new(0, 0, 0.8)
    inventoryTitle.Text = "🎒 INVENTÁRIO E FERRAMENTAS"
    inventoryTitle.TextColor3 = Color3.new(1, 1, 1)
    inventoryTitle.TextScaled = true
    inventoryTitle.Font = Enum.Font.SourceSansBold
    inventoryTitle.Parent = inventoryFrame
    
    local inventoryText = Instance.new("TextLabel")
    inventoryText.Name = "InventoryText"
    inventoryText.Size = UDim2.new(1, -10, 1, -35)
    inventoryText.Position = UDim2.new(0, 5, 0, 35)
    inventoryText.BackgroundTransparency = 1
    inventoryText.Text = ""
    inventoryText.TextColor3 = Color3.new(1, 1, 1)
    inventoryText.TextSize = 16
    inventoryText.TextWrapped = true
    inventoryText.TextXAlignment = Enum.TextXAlignment.Left
    inventoryText.TextYAlignment = Enum.TextYAlignment.Top
    inventoryText.Font = Enum.Font.SourceSans
    inventoryText.Parent = inventoryFrame
    
    -- === SEÇÃO CONQUISTAS ===
    local achievementsFrame = Instance.new("Frame")
    achievementsFrame.Size = UDim2.new(1, -20, 0, 160)
    achievementsFrame.Position = UDim2.new(0, 10, 0, 460)
    achievementsFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    achievementsFrame.BorderSizePixel = 2
    achievementsFrame.BorderColor3 = Color3.new(1, 1, 0)
    achievementsFrame.Parent = scrollFrame
    
    local achievementsTitle = Instance.new("TextLabel")
    achievementsTitle.Size = UDim2.new(1, 0, 0, 30)
    achievementsTitle.Position = UDim2.new(0, 0, 0, 0)
    achievementsTitle.BackgroundColor3 = Color3.new(0.8, 0.8, 0)
    achievementsTitle.Text = "🏆 CONQUISTAS"
    achievementsTitle.TextColor3 = Color3.new(0, 0, 0)
    achievementsTitle.TextScaled = true
    achievementsTitle.Font = Enum.Font.SourceSansBold
    achievementsTitle.Parent = achievementsFrame
    
    local achievementsText = Instance.new("TextLabel")
    achievementsText.Name = "AchievementsText"
    achievementsText.Size = UDim2.new(1, -10, 1, -35)
    achievementsText.Position = UDim2.new(0, 5, 0, 35)
    achievementsText.BackgroundTransparency = 1
    achievementsText.Text = ""
    achievementsText.TextColor3 = Color3.new(1, 1, 1)
    achievementsText.TextSize = 16
    achievementsText.TextWrapped = true
    achievementsText.TextXAlignment = Enum.TextXAlignment.Left
    achievementsText.TextYAlignment = Enum.TextYAlignment.Top
    achievementsText.Font = Enum.Font.SourceSans
    achievementsText.Parent = achievementsFrame
    
    -- === SEÇÃO ESTATÍSTICAS ===
    local statsFrame = Instance.new("Frame")
    statsFrame.Size = UDim2.new(1, -20, 0, 160)
    statsFrame.Position = UDim2.new(0, 10, 0, 630)
    statsFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    statsFrame.BorderSizePixel = 2
    statsFrame.BorderColor3 = Color3.new(1, 0, 1)
    statsFrame.Parent = scrollFrame
    
    local statsTitle = Instance.new("TextLabel")
    statsTitle.Size = UDim2.new(1, 0, 0, 30)
    statsTitle.Position = UDim2.new(0, 0, 0, 0)
    statsTitle.BackgroundColor3 = Color3.new(0.8, 0, 0.8)
    statsTitle.Text = "📈 ESTATÍSTICAS DE COMBATE"
    statsTitle.TextColor3 = Color3.new(1, 1, 1)
    statsTitle.TextScaled = true
    statsTitle.Font = Enum.Font.SourceSansBold
    statsTitle.Parent = statsFrame
    
    local statsText = Instance.new("TextLabel")
    statsText.Name = "StatsText"
    statsText.Size = UDim2.new(1, -10, 1, -35)
    statsText.Position = UDim2.new(0, 5, 0, 35)
    statsText.BackgroundTransparency = 1
    statsText.Text = ""
    statsText.TextColor3 = Color3.new(1, 1, 1)
    statsText.TextSize = 16
    statsText.TextWrapped = true
    statsText.TextXAlignment = Enum.TextXAlignment.Left
    statsText.TextYAlignment = Enum.TextYAlignment.Top
    statsText.Font = Enum.Font.SourceSans
    statsText.Parent = statsFrame
    
    -- Conecta botão fechar
    closeButton.MouseButton1Click:Connect(function()
        toggleMenu()
    end)
    
    return statusText, baseText, inventoryText, achievementsText, statsText
end

-- Função para alternar visibilidade do menu
function toggleMenu()
    if not mainFrame then return end

    isMenuOpen = not isMenuOpen
    mainFrame.Visible = isMenuOpen

    if isMenuOpen then
        print("📋 Menu do jogador aberto - Pressione TAB para fechar")
    else
        print("📋 Menu do jogador fechado")
    end
end

-- Função para atualizar dados do jogador
local function updatePlayerData()
    if not player.Character then return end

    -- Atualiza vida
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        playerData.health = math.floor(humanoid.Health)
        playerData.maxHealth = math.floor(humanoid.MaxHealth)
    end

    -- Atualiza base e parceiro
    playerData.baseName = "Nenhuma"
    playerData.hasPartner = false
    playerData.partnerName = ""
    
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")

            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerData.baseName = base.Name
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local materialsValue = base:FindFirstChild("BuildingMaterials")

                if baseSizeValue then 
                    playerData.baseSize = math.floor(baseSizeValue.Value)
                    if not playerData.achievements.firstBase and playerData.baseSize > 0 then
                        playerData.achievements.firstBase = true
                    end
                end
                if materialsValue then playerData.materials = materialsValue.Value end
                
                -- Verifica parceiro
                if owner and owner.Value == player and partner and partner.Value then
                    playerData.hasPartner = true
                    playerData.partnerName = partner.Value.Name
                    if not playerData.achievements.firstPartner then
                        playerData.achievements.firstPartner = true
                    end
                elseif partner and partner.Value == player and owner and owner.Value then
                    playerData.hasPartner = true
                    playerData.partnerName = owner.Value.Name
                    if not playerData.achievements.firstPartner then
                        playerData.achievements.firstPartner = true
                    end
                end
                break
            end
        end
    end

    -- Atualiza tempo jogado
    playerData.timePlayed = playerData.timePlayed + 1/60

    -- Verifica ferramentas no inventário
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    local hasCombatGun = false
    local hasCollectorGun = false
    playerData.carryingResources = 0

    if backpack then
        hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
    end

    if character then
        hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil

        -- Verifica se está carregando recursos
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if humanoidRootPart then
            for _, child in ipairs(humanoidRootPart:GetChildren()) do
                if child.Name == "CarryingResource" then
                    playerData.carryingResources = playerData.carryingResources + 1
                end
            end
            
            if playerData.carryingResources > 0 and not playerData.achievements.firstResource then
                playerData.achievements.firstResource = true
            end
        end
    end

    -- Atualiza conquistas baseadas em estatísticas
    if playerData.enemiesKilled > 0 and not playerData.achievements.firstKill then
        playerData.achievements.firstKill = true
    end
    
    if playerData.resourcesCollected >= 10 and not playerData.achievements.resourceCollector then
        playerData.achievements.resourceCollector = true
    end
    
    if playerData.basesDestroyed > 0 and not playerData.achievements.baseDestroyer then
        playerData.achievements.baseDestroyer = true
    end
end

-- Função para atualizar UI
local function updateUI(statusText, baseText, inventoryText, achievementsText, statsText)
    if not statusText or not statusText.Parent then return end

    -- Atualiza status vital
    local minutes = math.floor(playerData.timePlayed / 60)
    local seconds = math.floor(playerData.timePlayed % 60)
    local healthPercent = math.floor((playerData.health / playerData.maxHealth) * 100)
    
    statusText.Text = string.format(
        "❤️ Vida: %d/%d (%d%%)\n⏰ Tempo de Jogo: %d:%02d\n💀 Mortes: %d\n🎯 Status: %s",
        playerData.health, playerData.maxHealth, healthPercent,
        minutes, seconds,
        playerData.deathCount,
        playerData.health > 0 and "Vivo" or "Morto"
    )

    -- Atualiza base e recursos
    local basePercent = playerData.baseSize > 0 and math.floor((playerData.baseSize / playerData.maxBaseSize) * 100) or 0
    local partnerInfo = playerData.hasPartner and ("👥 Parceiro: " .. playerData.partnerName) or "👤 Jogando Solo"
    
    baseText.Text = string.format(
        "🏠 Base: %s\n📏 Tamanho: %d/%d (%d%%)\n🔧 Materiais: %d\n💎 Carregando: %d recursos\n%s",
        playerData.baseName,
        playerData.baseSize, playerData.maxBaseSize, basePercent,
        playerData.materials,
        playerData.carryingResources,
        partnerInfo
    )

    -- Atualiza inventário
    local combatStatus = (player.Backpack and player.Backpack:FindFirstChild("CombatGun")) or 
                        (player.Character and player.Character:FindFirstChild("CombatGun"))
    local collectorStatus = (player.Backpack and player.Backpack:FindFirstChild("CollectorGun")) or 
                           (player.Character and player.Character:FindFirstChild("CollectorGun"))
    
    inventoryText.Text = string.format(
        "🔫 CombatGun: %s\n🔨 CollectorGun: %s\n💎 Recursos Coletados: %d\n📦 Recursos Depositados: %d",
        combatStatus and "✅ Disponível" or "❌ Não encontrada",
        collectorStatus and "✅ Disponível" or "❌ Não encontrada",
        playerData.resourcesCollected,
        playerData.resourcesDeposited
    )

    -- Atualiza conquistas
    local achievementsList = {
        {"🎮 Bem-vindo à Arena!", playerData.achievements.welcomeGame},
        {"���� Primeira Base", playerData.achievements.firstBase},
        {"⚔️ Primeiro Combate", playerData.achievements.firstCombat},
        {"💎 Primeiro Recurso", playerData.achievements.firstResource},
        {"🤝 Primeira Dupla", playerData.achievements.firstPartner},
        {"💀 Primeira Eliminação", playerData.achievements.firstKill},
        {"🏆 Coletor de Recursos (10+)", playerData.achievements.resourceCollector},
        {"💥 Destruidor de Bases", playerData.achievements.baseDestroyer}
    }
    
    local achievementsStr = ""
    for i, achievement in ipairs(achievementsList) do
        local status = achievement[2] and "✅" or "⏳"
        achievementsStr = achievementsStr .. status .. " " .. achievement[1]
        if i < #achievementsList then
            achievementsStr = achievementsStr .. "\n"
        end
    end
    achievementsText.Text = achievementsStr

    -- Atualiza estatísticas
    local kdr = playerData.deathCount > 0 and (playerData.enemiesKilled / playerData.deathCount) or playerData.enemiesKilled
    
    statsText.Text = string.format(
        "💀 Eliminações: %d\n☠️ Mortes: %d\n📊 K/D Ratio: %.2f\n🏠 Bases Destruídas: %d\n🤝 Convites Enviados: %d\n⚔️ Combates Realizados: %d",
        playerData.enemiesKilled,
        playerData.deathCount,
        kdr,
        playerData.basesDestroyed,
        playerData.partnersInvited,
        playerData.enemiesKilled + playerData.deathCount
    )
end

-- Inicialização
local function initializeMenu()
    local statusText, baseText, inventoryText, achievementsText, statsText = createEnhancedMenu()

    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        if not player.Character then
            return
        end
        updatePlayerData()
        updateUI(statusText, baseText, inventoryText, achievementsText, statsText)
    end)

    -- Conecta tecla TAB
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.Tab then
            toggleMenu()
        end
    end)

    print("✅ Menu melhorado do jogador carregado! Pressione TAB para abrir/fechar")
end

-- Inicializa quando o jogador spawna
local function onCharacterAdded()
    task.wait(2)
    initializeMenu()
end

-- Conecta eventos
if player.Character then
    onCharacterAdded()
end

player.CharacterAdded:Connect(onCharacterAdded)

return true