-- ForceUI.client.lua
-- Força a criação da UI do jogo

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda o jogador carregar
if not player.Character then
    player.CharacterAdded:Wait()
end

wait(2) -- Aguarda um pouco para garantir que tudo carregou

-- Variáveis da UI
local screenGui = nil
local mainHUD = nil
local playerMenu = nil
local isMenuOpen = false

-- Função para criar HUD principal
local function createMainHUD()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove HUD existente
    local existingHUD = playerGui:FindFirstChild("MainHUD")
    if existingHUD then existingHUD:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainHUD"
    screenGui.Parent = playerGui
    
    -- === PAINEL PRINCIPAL ===
    mainHUD = Instance.new("Frame")
    mainHUD.Name = "MainPanel"
    mainHUD.Size = UDim2.new(0, 350, 0, 200)
    mainHUD.Position = UDim2.new(0, 10, 0, 10)
    mainHUD.BackgroundColor3 = Color3.new(0, 0, 0)
    mainHUD.BackgroundTransparency = 0.2
    mainHUD.BorderSizePixel = 2
    mainHUD.BorderColor3 = Color3.new(0, 1, 1)
    mainHUD.Parent = screenGui
    
    -- === SEÇÃO DE VIDA ===
    local healthSection = Instance.new("Frame")
    healthSection.Size = UDim2.new(1, -10, 0, 45)
    healthSection.Position = UDim2.new(0, 5, 0, 5)
    healthSection.BackgroundTransparency = 1
    healthSection.Parent = mainHUD
    
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0, 20)
    healthLabel.Position = UDim2.new(0, 0, 0, 0)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ VIDA"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = healthSection
    
    -- Background da barra de vida
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, 0, 0, 20)
    healthBarBG.Position = UDim2.new(0, 0, 0, 22)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = healthSection
    
    -- Barra de vida
    local healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(0, 1, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG
    
    -- Texto da vida
    local healthText = Instance.new("TextLabel")
    healthText.Name = "HealthText"
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.SourceSansBold
    healthText.Parent = healthBarBG
    
    -- === SEÇÃO DE ARMAS ===
    local weaponsSection = Instance.new("Frame")
    weaponsSection.Size = UDim2.new(1, -10, 0, 60)
    weaponsSection.Position = UDim2.new(0, 5, 0, 55)
    weaponsSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    weaponsSection.BorderSizePixel = 1
    weaponsSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    weaponsSection.Parent = mainHUD
    
    local weaponsTitle = Instance.new("TextLabel")
    weaponsTitle.Size = UDim2.new(1, 0, 0, 20)
    weaponsTitle.Position = UDim2.new(0, 0, 0, 0)
    weaponsTitle.BackgroundTransparency = 1
    weaponsTitle.Text = "🔫 ARMAS"
    weaponsTitle.TextColor3 = Color3.new(1, 1, 1)
    weaponsTitle.TextScaled = true
    weaponsTitle.Font = Enum.Font.SourceSansBold
    weaponsTitle.Parent = weaponsSection
    
    local weaponsStatus = Instance.new("TextLabel")
    weaponsStatus.Name = "WeaponsStatus"
    weaponsStatus.Size = UDim2.new(1, -10, 1, -25)
    weaponsStatus.Position = UDim2.new(0, 5, 0, 22)
    weaponsStatus.BackgroundTransparency = 1
    weaponsStatus.Text = "🔫 CombatGun: ❌\n🔨 CollectorGun: ❌"
    weaponsStatus.TextColor3 = Color3.new(1, 1, 1)
    weaponsStatus.TextScaled = true
    weaponsStatus.TextWrapped = true
    weaponsStatus.TextXAlignment = Enum.TextXAlignment.Left
    weaponsStatus.Font = Enum.Font.SourceSans
    weaponsStatus.Parent = weaponsSection
    
    -- === SEÇÃO DE BASE ===
    local baseSection = Instance.new("Frame")
    baseSection.Size = UDim2.new(1, -10, 0, 45)
    baseSection.Position = UDim2.new(0, 5, 0, 120)
    baseSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    baseSection.BorderSizePixel = 1
    baseSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    baseSection.Parent = mainHUD
    
    local baseTitle = Instance.new("TextLabel")
    baseTitle.Size = UDim2.new(1, 0, 0, 20)
    baseTitle.Position = UDim2.new(0, 0, 0, 0)
    baseTitle.BackgroundTransparency = 1
    baseTitle.Text = "🏠 BASE"
    baseTitle.TextColor3 = Color3.new(1, 1, 1)
    baseTitle.TextScaled = true
    baseTitle.Font = Enum.Font.SourceSansBold
    baseTitle.Parent = baseSection
    
    local baseText = Instance.new("TextLabel")
    baseText.Name = "BaseText"
    baseText.Size = UDim2.new(1, -10, 1, -25)
    baseText.Position = UDim2.new(0, 5, 0, 22)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Nenhuma Base - Toque ClaimPad!"
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextScaled = true
    baseText.TextWrapped = true
    baseText.TextXAlignment = Enum.TextXAlignment.Left
    baseText.Font = Enum.Font.SourceSans
    baseText.Parent = baseSection
    
    -- === INSTRUÇÕES ===
    local instructionsPanel = Instance.new("Frame")
    instructionsPanel.Size = UDim2.new(0, 300, 0, 100)
    instructionsPanel.Position = UDim2.new(1, -310, 0, 10)
    instructionsPanel.BackgroundColor3 = Color3.new(0, 0, 0)
    instructionsPanel.BackgroundTransparency = 0.3
    instructionsPanel.BorderSizePixel = 2
    instructionsPanel.BorderColor3 = Color3.new(1, 1, 0)
    instructionsPanel.Parent = screenGui
    
    local instructionsTitle = Instance.new("TextLabel")
    instructionsTitle.Size = UDim2.new(1, 0, 0, 25)
    instructionsTitle.Position = UDim2.new(0, 0, 0, 0)
    instructionsTitle.BackgroundColor3 = Color3.new(1, 1, 0)
    instructionsTitle.Text = "📋 CONTROLES"
    instructionsTitle.TextColor3 = Color3.new(0, 0, 0)
    instructionsTitle.TextScaled = true
    instructionsTitle.Font = Enum.Font.SourceSansBold
    instructionsTitle.Parent = instructionsPanel
    
    local instructionsText = Instance.new("TextLabel")
    instructionsText.Size = UDim2.new(1, -10, 1, -30)
    instructionsText.Position = UDim2.new(0, 5, 0, 30)
    instructionsText.BackgroundTransparency = 1
    instructionsText.Text = "TAB - Menu do Jogador\nClique - Usar arma\nToque ClaimPad - Reivindicar base"
    instructionsText.TextColor3 = Color3.new(1, 1, 1)
    instructionsText.TextScaled = true
    instructionsText.TextWrapped = true
    instructionsText.TextXAlignment = Enum.TextXAlignment.Left
    instructionsText.Font = Enum.Font.SourceSans
    instructionsText.Parent = instructionsPanel
    
    print("✅ HUD principal criado!")
end

-- Função para criar menu do jogador
local function createPlayerMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove menu existente
    local existingMenu = playerGui:FindFirstChild("PlayerMenu")
    if existingMenu then existingMenu:Destroy() end
    
    -- Cria menu do jogador
    playerMenu = Instance.new("Frame")
    playerMenu.Name = "PlayerMenu"
    playerMenu.Size = UDim2.new(0, 600, 0, 500)
    playerMenu.Position = UDim2.new(0.5, -300, 0.5, -250)
    playerMenu.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    playerMenu.BackgroundTransparency = 0.1
    playerMenu.BorderSizePixel = 3
    playerMenu.BorderColor3 = Color3.new(0, 1, 1)
    playerMenu.Visible = false
    playerMenu.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 60)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.Text = "🎮 MENU DO JOGADOR - " .. player.Name
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = playerMenu
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -55, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = playerMenu
    
    -- Conteúdo do menu
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 1, -80)
    contentFrame.Position = UDim2.new(0, 10, 0, 70)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = playerMenu
    
    local contentText = Instance.new("TextLabel")
    contentText.Name = "ContentText"
    contentText.Size = UDim2.new(1, 0, 1, 0)
    contentText.Position = UDim2.new(0, 0, 0, 0)
    contentText.BackgroundTransparency = 1
    contentText.Text = "📊 STATUS DO JOGADOR\n\n❤️ Vida: 100/100\n🏠 Base: Nenhuma\n🔫 Armas: Verificando...\n💎 Recursos: 0\n\n📋 INSTRUÇÕES:\n• Toque em ClaimPads para reivindicar bases\n• Use as armas para combate e coleta\n• Pressione TAB para abrir/fechar este menu"
    contentText.TextColor3 = Color3.new(1, 1, 1)
    contentText.TextSize = 18
    contentText.TextWrapped = true
    contentText.TextXAlignment = Enum.TextXAlignment.Left
    contentText.TextYAlignment = Enum.TextYAlignment.Top
    contentText.Font = Enum.Font.SourceSans
    contentText.Parent = contentFrame
    
    -- Conecta botão fechar
    closeButton.MouseButton1Click:Connect(function()
        playerMenu.Visible = false
        isMenuOpen = false
    end)
    
    print("✅ Menu do jogador criado!")
end

-- Fun��ão para alternar menu
local function toggleMenu()
    if not playerMenu then return end
    
    isMenuOpen = not isMenuOpen
    playerMenu.Visible = isMenuOpen
    
    if isMenuOpen then
        print("📋 Menu aberto - Pressione TAB para fechar")
    end
end

-- Função para atualizar informações
local function updateUI()
    if not screenGui then return end
    
    -- Atualiza vida
    local healthBar = screenGui:FindFirstChild("MainPanel"):FindFirstChild("HealthBar", true)
    local healthText = screenGui:FindFirstChild("MainPanel"):FindFirstChild("HealthText", true)
    
    if player.Character and player.Character:FindFirstChild("Humanoid") and healthBar and healthText then
        local humanoid = player.Character.Humanoid
        local health = math.floor(humanoid.Health)
        local maxHealth = math.floor(humanoid.MaxHealth)
        local healthRatio = health / maxHealth
        
        healthText.Text = health .. "/" .. maxHealth
        healthBar.Size = UDim2.new(healthRatio, 0, 1, 0)
        
        -- Cor da barra
        if healthRatio > 0.6 then
            healthBar.BackgroundColor3 = Color3.new(0, 1, 0)
        elseif healthRatio > 0.3 then
            healthBar.BackgroundColor3 = Color3.new(1, 1, 0)
        else
            healthBar.BackgroundColor3 = Color3.new(1, 0, 0)
        end
    end
    
    -- Atualiza status das armas
    local weaponsStatus = screenGui:FindFirstChild("MainPanel"):FindFirstChild("WeaponsStatus", true)
    if weaponsStatus then
        local backpack = player:FindFirstChild("Backpack")
        local character = player.Character

        local hasCombatGun = false
        local hasCollectorGun = false

        if backpack then
            hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
            hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
        end

        if character then
            hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
            hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil
        end

        local combatStatus = hasCombatGun and "✅" or "❌"
        local collectorStatus = hasCollectorGun and "✅" or "❌"

        weaponsStatus.Text = "🔫 CombatGun: " .. combatStatus .. "\n🔨 CollectorGun: " .. collectorStatus
    end
    
    -- Atualiza base
    local baseText = screenGui:FindFirstChild("MainPanel"):FindFirstChild("BaseText", true)
    if baseText then
        local hasBase = false
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")

                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    hasBase = true
                    baseText.Text = "🏠 " .. base.Name .. " (Sua base)"
                    break
                end
            end
        end
        
        if not hasBase then
            baseText.Text = "Nenhuma Base - Toque ClaimPad!"
        end
    end
end

-- Inicializa a UI
local function initializeUI()
    createMainHUD()
    createPlayerMenu()
    
    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        if player.Character then
            updateUI()
        end
    end)
    
    -- Conecta tecla TAB
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.Tab then
            toggleMenu()
        end
    end)
    
    print("✅ UI do jogo inicializada!")
end

-- Inicializa quando o personagem spawna
local function onCharacterAdded()
    wait(2)
    initializeUI()
end

-- Conecta eventos
if player.Character then
    onCharacterAdded()
end

player.CharacterAdded:Connect(onCharacterAdded)

print("✅ ForceUI carregado - UI será criada automaticamente!")