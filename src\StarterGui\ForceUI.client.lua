-- ForceUI.client.lua
-- Sistema completo de UI do jogo

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda o jogador carregar
if not player.Character then
    player.CharacterAdded:Wait()
end

wait(2) -- Aguarda um pouco para garantir que tudo carregou

-- Variáveis da UI
local screenGui = nil
local mainHUD = nil
local playerMenu = nil
local inventoryMenu = nil
local isMenuOpen = false
local isInventoryOpen = false

-- Função para criar HUD principal
local function createMainHUD()
    local playerGui = player:WaitForChild("PlayerGui")

    -- Remove HUD existente
    local existingHUD = playerGui:FindFirstChild("MainHUD")
    if existingHUD then existingHUD:Destroy() end

    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainHUD"
    screenGui.Parent = playerGui

    -- === PAINEL PRINCIPAL ===
    mainHUD = Instance.new("Frame")
    mainHUD.Name = "MainPanel"
    mainHUD.Size = UDim2.new(0, 400, 0, 280)
    mainHUD.Position = UDim2.new(0, 10, 0, 10)
    mainHUD.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
    mainHUD.BackgroundTransparency = 0.1
    mainHUD.BorderSizePixel = 3
    mainHUD.BorderColor3 = Color3.new(0, 0.8, 1)
    mainHUD.Parent = screenGui

    -- Adiciona cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainHUD

    -- === SEÇÃO DE VIDA ===
    local healthSection = Instance.new("Frame")
    healthSection.Size = UDim2.new(1, -20, 0, 60)
    healthSection.Position = UDim2.new(0, 10, 0, 10)
    healthSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
    healthSection.BorderSizePixel = 0
    healthSection.Parent = mainHUD

    local healthCorner = Instance.new("UICorner")
    healthCorner.CornerRadius = UDim.new(0, 8)
    healthCorner.Parent = healthSection

    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0, 25)
    healthLabel.Position = UDim2.new(0, 0, 0, 5)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ VIDA"
    healthLabel.TextColor3 = Color3.new(1, 0.3, 0.3)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.GothamBold
    healthLabel.Parent = healthSection

    -- Background da barra de vida
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, -20, 0, 25)
    healthBarBG.Position = UDim2.new(0, 10, 0, 30)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 0
    healthBarBG.Parent = healthSection

    local healthBarCorner = Instance.new("UICorner")
    healthBarCorner.CornerRadius = UDim.new(0, 6)
    healthBarCorner.Parent = healthBarBG

    -- Barra de vida
    local healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG

    local healthBarInnerCorner = Instance.new("UICorner")
    healthBarInnerCorner.CornerRadius = UDim.new(0, 6)
    healthBarInnerCorner.Parent = healthBar

    -- Texto da vida
    local healthText = Instance.new("TextLabel")
    healthText.Name = "HealthText"
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.GothamBold
    healthText.Parent = healthBarBG

    -- === SEÇÃO DE ARMAS ===
    local weaponsSection = Instance.new("Frame")
    weaponsSection.Size = UDim2.new(1, -20, 0, 80)
    weaponsSection.Position = UDim2.new(0, 10, 0, 80)
    weaponsSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
    weaponsSection.BorderSizePixel = 0
    weaponsSection.Parent = mainHUD

    local weaponsCorner = Instance.new("UICorner")
    weaponsCorner.CornerRadius = UDim.new(0, 8)
    weaponsCorner.Parent = weaponsSection

    local weaponsTitle = Instance.new("TextLabel")
    weaponsTitle.Size = UDim2.new(1, 0, 0, 25)
    weaponsTitle.Position = UDim2.new(0, 0, 0, 5)
    weaponsTitle.BackgroundTransparency = 1
    weaponsTitle.Text = "🔫 ARSENAL"
    weaponsTitle.TextColor3 = Color3.new(1, 0.7, 0.2)
    weaponsTitle.TextScaled = true
    weaponsTitle.Font = Enum.Font.GothamBold
    weaponsTitle.Parent = weaponsSection

    local weaponsStatus = Instance.new("TextLabel")
    weaponsStatus.Name = "WeaponsStatus"
    weaponsStatus.Size = UDim2.new(1, -20, 1, -35)
    weaponsStatus.Position = UDim2.new(0, 10, 0, 30)
    weaponsStatus.BackgroundTransparency = 1
    weaponsStatus.Text = "🔫 CombatGun: ❌\n🔨 CollectorGun: ❌"
    weaponsStatus.TextColor3 = Color3.new(0.9, 0.9, 0.9)
    weaponsStatus.TextScaled = true
    weaponsStatus.TextWrapped = true
    weaponsStatus.TextXAlignment = Enum.TextXAlignment.Left
    weaponsStatus.Font = Enum.Font.Gotham
    weaponsStatus.Parent = weaponsSection

    -- === SEÇÃO DE BASE ===
    local baseSection = Instance.new("Frame")
    baseSection.Size = UDim2.new(1, -20, 0, 60)
    baseSection.Position = UDim2.new(0, 10, 0, 170)
    baseSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
    baseSection.BorderSizePixel = 0
    baseSection.Parent = mainHUD

    local baseCorner = Instance.new("UICorner")
    baseCorner.CornerRadius = UDim.new(0, 8)
    baseCorner.Parent = baseSection

    local baseTitle = Instance.new("TextLabel")
    baseTitle.Size = UDim2.new(1, 0, 0, 25)
    baseTitle.Position = UDim2.new(0, 0, 0, 5)
    baseTitle.BackgroundTransparency = 1
    baseTitle.Text = "🏠 BASE"
    baseTitle.TextColor3 = Color3.new(0.3, 1, 0.3)
    baseTitle.TextScaled = true
    baseTitle.Font = Enum.Font.GothamBold
    baseTitle.Parent = baseSection

    local baseText = Instance.new("TextLabel")
    baseText.Name = "BaseText"
    baseText.Size = UDim2.new(1, -20, 1, -35)
    baseText.Position = UDim2.new(0, 10, 0, 30)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Nenhuma Base - Toque ClaimPad!"
    baseText.TextColor3 = Color3.new(0.9, 0.9, 0.9)
    baseText.TextScaled = true
    baseText.TextWrapped = true
    baseText.TextXAlignment = Enum.TextXAlignment.Left
    baseText.Font = Enum.Font.Gotham
    baseText.Parent = baseSection

    -- === SEÇÃO DE INVENTÁRIO ===
    local inventorySection = Instance.new("Frame")
    inventorySection.Size = UDim2.new(1, -20, 0, 40)
    inventorySection.Position = UDim2.new(0, 10, 0, 240)
    inventorySection.BackgroundColor3 = Color3.new(0.15, 0.1, 0.2)
    inventorySection.BorderSizePixel = 0
    inventorySection.Parent = mainHUD

    local inventoryCorner = Instance.new("UICorner")
    inventoryCorner.CornerRadius = UDim.new(0, 8)
    inventoryCorner.Parent = inventorySection

    local inventoryButton = Instance.new("TextButton")
    inventoryButton.Name = "InventoryButton"
    inventoryButton.Size = UDim2.new(1, 0, 1, 0)
    inventoryButton.Position = UDim2.new(0, 0, 0, 0)
    inventoryButton.BackgroundTransparency = 1
    inventoryButton.Text = "🎒 INVENTÁRIO (I)"
    inventoryButton.TextColor3 = Color3.new(0.8, 0.6, 1)
    inventoryButton.TextScaled = true
    inventoryButton.Font = Enum.Font.GothamBold
    inventoryButton.Parent = inventorySection

    -- === INSTRUÇÕES ===
    local instructionsPanel = Instance.new("Frame")
    instructionsPanel.Size = UDim2.new(0, 350, 0, 140)
    instructionsPanel.Position = UDim2.new(1, -360, 0, 10)
    instructionsPanel.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
    instructionsPanel.BackgroundTransparency = 0.1
    instructionsPanel.BorderSizePixel = 0
    instructionsPanel.Parent = screenGui

    local instructionsCorner = Instance.new("UICorner")
    instructionsCorner.CornerRadius = UDim.new(0, 12)
    instructionsCorner.Parent = instructionsPanel

    local instructionsTitle = Instance.new("TextLabel")
    instructionsTitle.Size = UDim2.new(1, 0, 0, 35)
    instructionsTitle.Position = UDim2.new(0, 0, 0, 0)
    instructionsTitle.BackgroundColor3 = Color3.new(1, 0.8, 0.2)
    instructionsTitle.Text = "📋 CONTROLES"
    instructionsTitle.TextColor3 = Color3.new(0, 0, 0)
    instructionsTitle.TextScaled = true
    instructionsTitle.Font = Enum.Font.GothamBold
    instructionsTitle.Parent = instructionsPanel

    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = instructionsTitle

    local instructionsText = Instance.new("TextLabel")
    instructionsText.Size = UDim2.new(1, -20, 1, -45)
    instructionsText.Position = UDim2.new(0, 10, 0, 40)
    instructionsText.BackgroundTransparency = 1
    instructionsText.Text = "TAB - Menu do Jogador\nI - Inventário\nClique - Usar arma\nToque ClaimPad - Reivindicar base\nWASD - Movimento"
    instructionsText.TextColor3 = Color3.new(0.9, 0.9, 0.9)
    instructionsText.TextSize = 16
    instructionsText.TextWrapped = true
    instructionsText.TextXAlignment = Enum.TextXAlignment.Left
    instructionsText.TextYAlignment = Enum.TextYAlignment.Top
    instructionsText.Font = Enum.Font.Gotham
    instructionsText.Parent = instructionsPanel

    print("✅ HUD principal criado!")
end

-- Função para criar menu do jogador
local function createPlayerMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove menu existente
    local existingMenu = playerGui:FindFirstChild("PlayerMenu")
    if existingMenu then existingMenu:Destroy() end
    
    -- Cria menu do jogador
    playerMenu = Instance.new("Frame")
    playerMenu.Name = "PlayerMenu"
    playerMenu.Size = UDim2.new(0, 600, 0, 500)
    playerMenu.Position = UDim2.new(0.5, -300, 0.5, -250)
    playerMenu.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    playerMenu.BackgroundTransparency = 0.1
    playerMenu.BorderSizePixel = 3
    playerMenu.BorderColor3 = Color3.new(0, 1, 1)
    playerMenu.Visible = false
    playerMenu.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 60)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.Text = "🎮 MENU DO JOGADOR - " .. player.Name
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = playerMenu
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -55, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = playerMenu
    
    -- Conteúdo do menu
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 1, -80)
    contentFrame.Position = UDim2.new(0, 10, 0, 70)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = playerMenu
    
    local contentText = Instance.new("TextLabel")
    contentText.Name = "ContentText"
    contentText.Size = UDim2.new(1, 0, 1, 0)
    contentText.Position = UDim2.new(0, 0, 0, 0)
    contentText.BackgroundTransparency = 1
    contentText.Text = "📊 STATUS DO JOGADOR\n\n❤️ Vida: 100/100\n🏠 Base: Nenhuma\n🔫 Armas: Verificando...\n💎 Recursos: 0\n\n📋 INSTRUÇÕES:\n• Toque em ClaimPads para reivindicar bases\n• Use as armas para combate e coleta\n• Pressione TAB para abrir/fechar este menu"
    contentText.TextColor3 = Color3.new(1, 1, 1)
    contentText.TextSize = 18
    contentText.TextWrapped = true
    contentText.TextXAlignment = Enum.TextXAlignment.Left
    contentText.TextYAlignment = Enum.TextYAlignment.Top
    contentText.Font = Enum.Font.SourceSans
    contentText.Parent = contentFrame
    
    -- Conecta botão fechar
    closeButton.MouseButton1Click:Connect(function()
        playerMenu.Visible = false
        isMenuOpen = false
    end)
    
    print("✅ Menu do jogador criado!")
end

-- Função para criar inventário
local function createInventory()
    local playerGui = player:WaitForChild("PlayerGui")

    -- Remove inventário existente
    local existingInventory = playerGui:FindFirstChild("InventoryMenu")
    if existingInventory then existingInventory:Destroy() end

    -- Cria inventário
    inventoryMenu = Instance.new("Frame")
    inventoryMenu.Name = "InventoryMenu"
    inventoryMenu.Size = UDim2.new(0, 700, 0, 600)
    inventoryMenu.Position = UDim2.new(0.5, -350, 0.5, -300)
    inventoryMenu.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
    inventoryMenu.BackgroundTransparency = 0.05
    inventoryMenu.BorderSizePixel = 0
    inventoryMenu.Visible = false
    inventoryMenu.Parent = screenGui

    local inventoryCorner = Instance.new("UICorner")
    inventoryCorner.CornerRadius = UDim.new(0, 15)
    inventoryCorner.Parent = inventoryMenu

    -- Título do inventário
    local inventoryTitle = Instance.new("TextLabel")
    inventoryTitle.Size = UDim2.new(1, 0, 0, 60)
    inventoryTitle.Position = UDim2.new(0, 0, 0, 0)
    inventoryTitle.BackgroundColor3 = Color3.new(0.8, 0.6, 1)
    inventoryTitle.Text = "🎒 INVENTÁRIO - " .. player.Name
    inventoryTitle.TextColor3 = Color3.new(0, 0, 0)
    inventoryTitle.TextScaled = true
    inventoryTitle.Font = Enum.Font.GothamBold
    inventoryTitle.Parent = inventoryMenu

    local inventoryTitleCorner = Instance.new("UICorner")
    inventoryTitleCorner.CornerRadius = UDim.new(0, 15)
    inventoryTitleCorner.Parent = inventoryTitle

    -- Botão fechar inventário
    local closeInventoryButton = Instance.new("TextButton")
    closeInventoryButton.Size = UDim2.new(0, 50, 0, 50)
    closeInventoryButton.Position = UDim2.new(1, -55, 0, 5)
    closeInventoryButton.BackgroundColor3 = Color3.new(1, 0.3, 0.3)
    closeInventoryButton.Text = "✕"
    closeInventoryButton.TextColor3 = Color3.new(1, 1, 1)
    closeInventoryButton.TextScaled = true
    closeInventoryButton.Font = Enum.Font.GothamBold
    closeInventoryButton.Parent = inventoryMenu

    local closeButtonCorner = Instance.new("UICorner")
    closeButtonCorner.CornerRadius = UDim.new(0, 10)
    closeButtonCorner.Parent = closeInventoryButton

    -- Área de conteúdo do inventário
    local inventoryContent = Instance.new("ScrollingFrame")
    inventoryContent.Size = UDim2.new(1, -20, 1, -80)
    inventoryContent.Position = UDim2.new(0, 10, 0, 70)
    inventoryContent.BackgroundTransparency = 1
    inventoryContent.ScrollBarThickness = 8
    inventoryContent.Parent = inventoryMenu

    -- Grid layout para itens
    local gridLayout = Instance.new("UIGridLayout")
    gridLayout.CellSize = UDim2.new(0, 120, 0, 120)
    gridLayout.CellPadding = UDim2.new(0, 10, 0, 10)
    gridLayout.Parent = inventoryContent

    -- Conecta botão fechar
    closeInventoryButton.MouseButton1Click:Connect(function()
        inventoryMenu.Visible = false
        isInventoryOpen = false
    end)

    print("✅ Inventário criado!")
end

-- Função para alternar menu
local function toggleMenu()
    if not playerMenu then return end

    isMenuOpen = not isMenuOpen
    playerMenu.Visible = isMenuOpen

    if isMenuOpen then
        print("📋 Menu aberto - Pressione TAB para fechar")
    end
end

-- Função para alternar inventário
local function toggleInventory()
    if not inventoryMenu then return end

    isInventoryOpen = not isInventoryOpen
    inventoryMenu.Visible = isInventoryOpen

    if isInventoryOpen then
        print("🎒 Inventário aberto - Pressione I para fechar")
    end
end

-- Função para atualizar informações
local function updateUI()
    if not screenGui then return end
    
    -- Atualiza vida
    local healthBar = screenGui:FindFirstChild("MainPanel"):FindFirstChild("HealthBar", true)
    local healthText = screenGui:FindFirstChild("MainPanel"):FindFirstChild("HealthText", true)
    
    if player.Character and player.Character:FindFirstChild("Humanoid") and healthBar and healthText then
        local humanoid = player.Character.Humanoid
        local health = math.floor(humanoid.Health)
        local maxHealth = math.floor(humanoid.MaxHealth)
        local healthRatio = health / maxHealth
        
        healthText.Text = health .. "/" .. maxHealth
        healthBar.Size = UDim2.new(healthRatio, 0, 1, 0)
        
        -- Cor da barra
        if healthRatio > 0.6 then
            healthBar.BackgroundColor3 = Color3.new(0, 1, 0)
        elseif healthRatio > 0.3 then
            healthBar.BackgroundColor3 = Color3.new(1, 1, 0)
        else
            healthBar.BackgroundColor3 = Color3.new(1, 0, 0)
        end
    end
    
    -- Atualiza status das armas
    local weaponsStatus = screenGui:FindFirstChild("MainPanel"):FindFirstChild("WeaponsStatus", true)
    if weaponsStatus then
        local backpack = player:FindFirstChild("Backpack")
        local character = player.Character

        local hasCombatGun = false
        local hasCollectorGun = false

        if backpack then
            hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
            hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
        end

        if character then
            hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
            hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil
        end

        local combatStatus = hasCombatGun and "✅" or "❌"
        local collectorStatus = hasCollectorGun and "✅" or "❌"

        weaponsStatus.Text = "🔫 CombatGun: " .. combatStatus .. "\n🔨 CollectorGun: " .. collectorStatus
    end
    
    -- Atualiza base
    local baseText = screenGui:FindFirstChild("MainPanel"):FindFirstChild("BaseText", true)
    if baseText then
        local hasBase = false
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")

                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    hasBase = true
                    baseText.Text = "🏠 " .. base.Name .. " (Sua base)"
                    break
                end
            end
        end
        
        if not hasBase then
            baseText.Text = "Nenhuma Base - Toque ClaimPad!"
        end
    end
end

-- Inicializa a UI
local function initializeUI()
    createMainHUD()
    createPlayerMenu()
    createInventory()

    -- Conecta botão do inventário no HUD
    local inventoryButton = screenGui:FindFirstChild("MainPanel"):FindFirstChild("InventoryButton", true)
    if inventoryButton then
        inventoryButton.MouseButton1Click:Connect(function()
            toggleInventory()
        end)
    end

    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        if player.Character then
            updateUI()
        end
    end)

    -- Conecta teclas
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.Tab then
            toggleMenu()
        elseif input.KeyCode == Enum.KeyCode.I then
            toggleInventory()
        end
    end)

    print("✅ UI do jogo inicializada!")
end

-- Inicializa quando o personagem spawna
local function onCharacterAdded()
    wait(2)
    initializeUI()
end

-- Conecta eventos
if player.Character then
    onCharacterAdded()
end

player.CharacterAdded:Connect(onCharacterAdded)

print("✅ ForceUI carregado - UI será criada automaticamente!")