-- ImprovedHUD.lua
-- HUD melhorado sempre visível com informações essenciais

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

-- Variáveis da UI
local screenGui = nil
local healthBar = nil
local healthText = nil
local baseBar = nil
local baseText = nil
local weaponsStatus = nil
local resourcesText = nil

-- Função para criar o HUD melhorado
local function createImprovedHUD()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove HUD existente
    local existingHUD = playerGui:FindFirstChild("ImprovedHUD")
    if existingHUD then existingHUD:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "ImprovedHUD"
    screenGui.Parent = playerGui
    
    -- === PAINEL PRINCIPAL ===
    local mainPanel = Instance.new("Frame")
    mainPanel.Name = "MainPanel"
    mainPanel.Size = UDim2.new(0, 350, 0, 200)
    mainPanel.Position = UDim2.new(0, 10, 0, 10)
    mainPanel.BackgroundColor3 = Color3.new(0, 0, 0)
    mainPanel.BackgroundTransparency = 0.2
    mainPanel.BorderSizePixel = 2
    mainPanel.BorderColor3 = Color3.new(0, 1, 1)
    mainPanel.Parent = screenGui
    
    -- Efeito de borda brilhante
    local borderGlow = Instance.new("UIStroke")
    borderGlow.Color = Color3.new(0, 1, 1)
    borderGlow.Thickness = 1
    borderGlow.Transparency = 0.5
    borderGlow.Parent = mainPanel
    
    -- === SEÇÃO DE VIDA ===
    local healthSection = Instance.new("Frame")
    healthSection.Size = UDim2.new(1, -10, 0, 45)
    healthSection.Position = UDim2.new(0, 5, 0, 5)
    healthSection.BackgroundTransparency = 1
    healthSection.Parent = mainPanel
    
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0, 20)
    healthLabel.Position = UDim2.new(0, 0, 0, 0)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ VIDA"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = healthSection
    
    -- Background da barra de vida
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, 0, 0, 20)
    healthBarBG.Position = UDim2.new(0, 0, 0, 22)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = healthSection
    
    -- Barra de vida
    healthBar = Instance.new("Frame")
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(0, 1, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG
    
    -- Texto da vida
    healthText = Instance.new("TextLabel")
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.SourceSansBold
    healthText.Parent = healthBarBG
    
    -- === SEÇÃO DA BASE ===
    local baseSection = Instance.new("Frame")
    baseSection.Size = UDim2.new(1, -10, 0, 45)
    baseSection.Position = UDim2.new(0, 5, 0, 55)
    baseSection.BackgroundTransparency = 1
    baseSection.Parent = mainPanel
    
    local baseLabel = Instance.new("TextLabel")
    baseLabel.Size = UDim2.new(1, 0, 0, 20)
    baseLabel.Position = UDim2.new(0, 0, 0, 0)
    baseLabel.BackgroundTransparency = 1
    baseLabel.Text = "🏠 BASE"
    baseLabel.TextColor3 = Color3.new(1, 1, 1)
    baseLabel.TextScaled = true
    baseLabel.Font = Enum.Font.SourceSansBold
    baseLabel.Parent = baseSection
    
    -- Background da barra da base
    local baseBarBG = Instance.new("Frame")
    baseBarBG.Size = UDim2.new(1, 0, 0, 20)
    baseBarBG.Position = UDim2.new(0, 0, 0, 22)
    baseBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    baseBarBG.BorderSizePixel = 1
    baseBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    baseBarBG.Parent = baseSection
    
    -- Barra da base
    baseBar = Instance.new("Frame")
    baseBar.Size = UDim2.new(0, 0, 1, 0)
    baseBar.Position = UDim2.new(0, 0, 0, 0)
    baseBar.BackgroundColor3 = Color3.new(1, 1, 0)
    baseBar.BorderSizePixel = 0
    baseBar.Parent = baseBarBG
    
    -- Texto da base
    baseText = Instance.new("TextLabel")
    baseText.Size = UDim2.new(1, 0, 1, 0)
    baseText.Position = UDim2.new(0, 0, 0, 0)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Nenhuma Base"
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextScaled = true
    baseText.Font = Enum.Font.SourceSansBold
    baseText.Parent = baseBarBG
    
    -- === SEÇÃO DE RECURSOS ===
    local resourcesSection = Instance.new("Frame")
    resourcesSection.Size = UDim2.new(1, -10, 0, 25)
    resourcesSection.Position = UDim2.new(0, 5, 0, 105)
    resourcesSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    resourcesSection.BorderSizePixel = 1
    resourcesSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    resourcesSection.Parent = mainPanel
    
    resourcesText = Instance.new("TextLabel")
    resourcesText.Size = UDim2.new(1, -10, 1, 0)
    resourcesText.Position = UDim2.new(0, 5, 0, 0)
    resourcesText.BackgroundTransparency = 1
    resourcesText.Text = "💎 Recursos: 0 | 🔧 Materiais: 0"
    resourcesText.TextColor3 = Color3.new(1, 1, 1)
    resourcesText.TextScaled = true
    resourcesText.Font = Enum.Font.SourceSans
    resourcesText.TextXAlignment = Enum.TextXAlignment.Left
    resourcesText.Parent = resourcesSection
    
    -- === SEÇÃO DE ARMAS ===
    local weaponsSection = Instance.new("Frame")
    weaponsSection.Size = UDim2.new(1, -10, 0, 60)
    weaponsSection.Position = UDim2.new(0, 5, 0, 135)
    weaponsSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    weaponsSection.BorderSizePixel = 1
    weaponsSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    weaponsSection.Parent = mainPanel
    
    local weaponsTitle = Instance.new("TextLabel")
    weaponsTitle.Size = UDim2.new(1, 0, 0, 20)
    weaponsTitle.Position = UDim2.new(0, 0, 0, 0)
    weaponsTitle.BackgroundTransparency = 1
    weaponsTitle.Text = "🔫 ARMAS"
    weaponsTitle.TextColor3 = Color3.new(1, 1, 1)
    weaponsTitle.TextScaled = true
    weaponsTitle.Font = Enum.Font.SourceSansBold
    weaponsTitle.Parent = weaponsSection
    
    weaponsStatus = Instance.new("TextLabel")
    weaponsStatus.Size = UDim2.new(1, -10, 1, -25)
    weaponsStatus.Position = UDim2.new(0, 5, 0, 22)
    weaponsStatus.BackgroundTransparency = 1
    weaponsStatus.Text = "🔫 CombatGun: ❌\n🔨 CollectorGun: ❌"
    weaponsStatus.TextColor3 = Color3.new(1, 1, 1)
    weaponsStatus.TextScaled = true
    weaponsStatus.TextWrapped = true
    weaponsStatus.TextXAlignment = Enum.TextXAlignment.Left
    weaponsStatus.Font = Enum.Font.SourceSans
    weaponsStatus.Parent = weaponsSection
    
    -- === PAINEL DE INSTRUÇÕES ===
    local instructionsPanel = Instance.new("Frame")
    instructionsPanel.Name = "InstructionsPanel"
    instructionsPanel.Size = UDim2.new(0, 300, 0, 120)
    instructionsPanel.Position = UDim2.new(1, -310, 0, 10)
    instructionsPanel.BackgroundColor3 = Color3.new(0, 0, 0)
    instructionsPanel.BackgroundTransparency = 0.3
    instructionsPanel.BorderSizePixel = 2
    instructionsPanel.BorderColor3 = Color3.new(1, 1, 0)
    instructionsPanel.Parent = screenGui
    
    local instructionsTitle = Instance.new("TextLabel")
    instructionsTitle.Size = UDim2.new(1, 0, 0, 25)
    instructionsTitle.Position = UDim2.new(0, 0, 0, 0)
    instructionsTitle.BackgroundColor3 = Color3.new(1, 1, 0)
    instructionsTitle.Text = "📋 CONTROLES"
    instructionsTitle.TextColor3 = Color3.new(0, 0, 0)
    instructionsTitle.TextScaled = true
    instructionsTitle.Font = Enum.Font.SourceSansBold
    instructionsTitle.Parent = instructionsPanel
    
    local instructionsText = Instance.new("TextLabel")
    instructionsText.Size = UDim2.new(1, -10, 1, -30)
    instructionsText.Position = UDim2.new(0, 5, 0, 30)
    instructionsText.BackgroundTransparency = 1
    instructionsText.Text = "TAB - Menu do Jogador\nB - Construir (na base)\nClique - Usar arma equipada\nToque ClaimPad - Reivindicar base"
    instructionsText.TextColor3 = Color3.new(1, 1, 1)
    instructionsText.TextScaled = true
    instructionsText.TextWrapped = true
    instructionsText.TextXAlignment = Enum.TextXAlignment.Left
    instructionsText.Font = Enum.Font.SourceSans
    instructionsText.Parent = instructionsPanel
end

-- Função para atualizar a vida
local function updateHealth()
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    local health = math.floor(humanoid.Health)
    local maxHealth = math.floor(humanoid.MaxHealth)
    local healthRatio = health / maxHealth
    
    -- Atualiza texto
    healthText.Text = health .. "/" .. maxHealth .. " (" .. math.floor(healthRatio * 100) .. "%)"
    
    -- Atualiza barra com animação
    local targetSize = UDim2.new(healthRatio, 0, 1, 0)
    local tween = TweenService:Create(healthBar, TweenInfo.new(0.3), {Size = targetSize})
    tween:Play()
    
    -- Muda cor baseada na vida
    if healthRatio > 0.6 then
        healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    elseif healthRatio > 0.3 then
        healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end
end

-- Função para atualizar informações da base
local function updateBaseInfo()
    local hasBase = false
    local baseName = "Nenhuma Base"
    local baseSize = 0
    local maxBaseSize = 500
    local materials = 0
    
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")

            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                hasBase = true
                baseName = base.Name
                
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local materialsValue = base:FindFirstChild("BuildingMaterials")

                if baseSizeValue then baseSize = math.floor(baseSizeValue.Value) end
                if materialsValue then materials = materialsValue.Value end
                break
            end
        end
    end
    
    if hasBase then
        local baseRatio = baseSize / maxBaseSize
        baseText.Text = baseName .. ": " .. baseSize .. "/" .. maxBaseSize .. " (" .. math.floor(baseRatio * 100) .. "%)"
        
        -- Atualiza barra da base com animação
        local targetSize = UDim2.new(baseRatio, 0, 1, 0)
        local tween = TweenService:Create(baseBar, TweenInfo.new(0.5), {Size = targetSize})
        tween:Play()
        
        -- Muda cor baseada no tamanho
        if baseRatio > 0.6 then
            baseBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
        elseif baseRatio > 0.3 then
            baseBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
        else
            baseBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
        end
    else
        baseText.Text = "Nenhuma Base - Toque em um ClaimPad!"
        baseBar.Size = UDim2.new(0, 0, 1, 0)
    end
    
    -- Atualiza recursos
    local carryingResources = 0
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        for _, child in ipairs(player.Character.HumanoidRootPart:GetChildren()) do
            if child.Name == "CarryingResource" then
                carryingResources = carryingResources + 1
            end
        end
    end
    
    resourcesText.Text = "💎 Carregando: " .. carryingResources .. " | 🔧 Materiais: " .. materials
end

-- Função para atualizar status das armas
local function updateWeaponsStatus()
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    local hasCombatGun = false
    local hasCollectorGun = false

    if backpack then
        hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
    end

    if character then
        hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil
    end

    local combatStatus = hasCombatGun and "✅" or "❌"
    local collectorStatus = hasCollectorGun and "✅" or "❌"

    weaponsStatus.Text = "🔫 CombatGun: " .. combatStatus .. "\n🔨 CollectorGun: " .. collectorStatus
end

-- Inicializa o HUD
local function initializeHUD()
    createImprovedHUD()
    
    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end
        
        updateHealth()
        updateBaseInfo()
        updateWeaponsStatus()
    end)
end

-- Conecta evento de atualização da base
updateBaseInfo.OnClientEvent:Connect(function(baseName, baseSize, buildingMaterials)
    -- Este evento pode ser usado para atualizações específicas do servidor
end)

-- Inicializa quando o jogador spawna
if player.Character then
    initializeHUD()
end

player.CharacterAdded:Connect(function()
    wait(1)
    initializeHUD()
end)

print("✅ HUD melhorado carregado com sucesso!")

return true