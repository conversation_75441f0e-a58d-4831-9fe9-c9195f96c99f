-- InstructionsUI.lua
-- Mostra instruções para o jogador sobre o sistema de construção

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui")

-- Cria a interface de instruções
local function createInstructionsGUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InstructionsGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Frame principal
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 350, 0, 200)
    mainFrame.Position = UDim2.new(0, 10, 1, -210)
    mainFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    mainFrame.BackgroundTransparency = 0.3
    mainFrame.BorderSizePixel = 2
    mainFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    mainFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, 0, 0, 30)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "CONTROLES DE CONSTRUÇÃO"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Lista de instruções
    local instructions = {
        "E - Abrir/Fechar menu de construção",
        "Clique - Selecionar/Mover construção",
        "Delete - Remover construção selecionada",
        "Escape - Cancelar movimento",
        "",
        "• Construções só podem ser feitas dentro da barreira",
        "• Colete recursos no mapa para obter materiais",
        "• Construções fora da barreira são destruídas"
    }
    
    for i, instruction in ipairs(instructions) do
        local instructionLabel = Instance.new("TextLabel")
        instructionLabel.Name = "Instruction" .. i
        instructionLabel.Size = UDim2.new(1, -10, 0, 18)
        instructionLabel.Position = UDim2.new(0, 5, 0, 25 + (i * 18))
        instructionLabel.BackgroundTransparency = 1
        instructionLabel.Text = instruction
        instructionLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
        instructionLabel.TextScaled = true
        instructionLabel.Font = Enum.Font.SourceSans
        instructionLabel.TextXAlignment = Enum.TextXAlignment.Left
        instructionLabel.Parent = mainFrame
        
        if instruction == "" then
            instructionLabel.Text = ""
        elseif instruction:sub(1, 1) == "•" then
            instructionLabel.TextColor3 = Color3.new(1, 1, 0)
        end
    end
    
    -- Botão para minimizar/maximizar
    local toggleButton = Instance.new("TextButton")
    toggleButton.Name = "ToggleButton"
    toggleButton.Size = UDim2.new(0, 20, 0, 20)
    toggleButton.Position = UDim2.new(1, -25, 0, 5)
    toggleButton.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
    toggleButton.BorderSizePixel = 0
    toggleButton.Text = "-"
    toggleButton.TextColor3 = Color3.new(1, 1, 1)
    toggleButton.TextScaled = true
    toggleButton.Font = Enum.Font.SourceSansBold
    toggleButton.Parent = mainFrame
    
    local isMinimized = false
    local originalSize = mainFrame.Size
    local minimizedSize = UDim2.new(0, 350, 0, 30)
    
    toggleButton.MouseButton1Click:Connect(function()
        isMinimized = not isMinimized
        
        local targetSize = isMinimized and minimizedSize or originalSize
        local targetPosition = isMinimized and UDim2.new(0, 10, 1, -40) or UDim2.new(0, 10, 1, -210)
        
        local tween = TweenService:Create(
            mainFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Size = targetSize, Position = targetPosition}
        )
        
        tween:Play()
        
        toggleButton.Text = isMinimized and "+" or "-"
        
        -- Esconde/mostra instruções
        for i = 2, #mainFrame:GetChildren() do
            local child = mainFrame:GetChildren()[i]
            if child ~= toggleButton and child ~= titleLabel then
                child.Visible = not isMinimized
            end
        end
    end)
    
    return screenGui
end

-- Inicializa a interface
createInstructionsGUI()

print("InstructionsUI inicializado com sucesso!")