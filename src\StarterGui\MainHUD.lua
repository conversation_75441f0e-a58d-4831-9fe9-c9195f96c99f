-- MainHUD.lua
-- HUD principal completo e funcional para o jogo de arena

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

-- Variáveis da UI
local screenGui = nil
local healthBar = nil
local healthText = nil
local baseBar = nil
local baseText = nil
local materialsText = nil
local resourcesCarriedText = nil
local statusIndicator = nil
local dashCooldownFrame = nil
local dashCooldownBar = nil
local dashCooldownText = nil
local speedIndicator = nil
local barrierStatusFrame = nil

-- Variáveis de estado
local isCarryingResources = false
local resourcesCarried = 0
local isSlowed = false
local dashCooldownActive = false
local dashCooldownTime = 0
local isInBarrier = false
local isInOwnBarrier = false

-- Função para criar o HUD
local function createHUD()
    local playerGui = player:WaitForChild("PlayerGui")

    -- Remove HUD existente
    local existingHUD = playerGui:FindFirstChild("MainHUD")
    if existingHUD then existingHUD:Destroy() end

    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainHUD"
    screenGui.Parent = playerGui

    -- Frame principal do HUD (expandido)
    local hudFrame = Instance.new("Frame")
    hudFrame.Name = "HUDFrame"
    hudFrame.Size = UDim2.new(0, 350, 0, 200)
    hudFrame.Position = UDim2.new(0, 10, 0, 10)
    hudFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    hudFrame.BackgroundTransparency = 0.2
    hudFrame.BorderSizePixel = 2
    hudFrame.BorderColor3 = Color3.new(0, 1, 1)
    hudFrame.Parent = screenGui

    -- Adiciona cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = hudFrame
    
    -- === BARRA DE VIDA ===
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0, 20)
    healthLabel.Position = UDim2.new(0, 0, 0, 5)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ VIDA"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = hudFrame
    
    -- Background da barra de vida
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, -10, 0, 15)
    healthBarBG.Position = UDim2.new(0, 5, 0, 25)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = hudFrame
    
    -- Barra de vida
    healthBar = Instance.new("Frame")
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(1, 0, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG
    
    -- Texto da vida
    healthText = Instance.new("TextLabel")
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.SourceSansBold
    healthText.Parent = healthBarBG
    
    -- === BARRA DA BASE ===
    local baseLabel = Instance.new("TextLabel")
    baseLabel.Size = UDim2.new(1, 0, 0, 20)
    baseLabel.Position = UDim2.new(0, 0, 0, 45)
    baseLabel.BackgroundTransparency = 1
    baseLabel.Text = "🏠 BASE"
    baseLabel.TextColor3 = Color3.new(1, 1, 1)
    baseLabel.TextScaled = true
    baseLabel.Font = Enum.Font.SourceSansBold
    baseLabel.Parent = hudFrame
    
    -- Background da barra da base
    local baseBarBG = Instance.new("Frame")
    baseBarBG.Size = UDim2.new(1, -10, 0, 15)
    baseBarBG.Position = UDim2.new(0, 5, 0, 65)
    baseBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    baseBarBG.BorderSizePixel = 1
    baseBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    baseBarBG.Parent = hudFrame
    
    -- Barra da base
    baseBar = Instance.new("Frame")
    baseBar.Size = UDim2.new(0.2, 0, 1, 0) -- Começa com 20% (100/500)
    baseBar.Position = UDim2.new(0, 0, 0, 0)
    baseBar.BackgroundColor3 = Color3.new(0, 1, 0)
    baseBar.BorderSizePixel = 0
    baseBar.Parent = baseBarBG
    
    -- Texto da base
    baseText = Instance.new("TextLabel")
    baseText.Size = UDim2.new(1, 0, 1, 0)
    baseText.Position = UDim2.new(0, 0, 0, 0)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Nenhuma Base"
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextScaled = true
    baseText.Font = Enum.Font.SourceSansBold
    baseText.Parent = baseBarBG
    
    -- === MATERIAIS DE CONSTRUÇÃO ===
    local materialsFrame = Instance.new("Frame")
    materialsFrame.Size = UDim2.new(1, -10, 0, 20)
    materialsFrame.Position = UDim2.new(0, 5, 0, 85)
    materialsFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    materialsFrame.BorderSizePixel = 1
    materialsFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    materialsFrame.Parent = hudFrame

    -- Ícone de materiais
    local materialsIcon = Instance.new("TextLabel")
    materialsIcon.Size = UDim2.new(0, 20, 1, 0)
    materialsIcon.Position = UDim2.new(0, 0, 0, 0)
    materialsIcon.BackgroundTransparency = 1
    materialsIcon.Text = "🔧"
    materialsIcon.TextColor3 = Color3.new(1, 1, 1)
    materialsIcon.TextScaled = true
    materialsIcon.Font = Enum.Font.SourceSansBold
    materialsIcon.Parent = materialsFrame

    -- Texto dos materiais
    materialsText = Instance.new("TextLabel")
    materialsText.Size = UDim2.new(1, -25, 1, 0)
    materialsText.Position = UDim2.new(0, 25, 0, 0)
    materialsText.BackgroundTransparency = 1
    materialsText.Text = "Materiais: 0"
    materialsText.TextColor3 = Color3.new(1, 1, 1)
    materialsText.TextScaled = true
    materialsText.Font = Enum.Font.SourceSansBold
    materialsText.TextXAlignment = Enum.TextXAlignment.Left
    materialsText.Parent = materialsFrame

    -- === RECURSOS CARREGADOS ===
    local resourcesFrame = Instance.new("Frame")
    resourcesFrame.Size = UDim2.new(1, -10, 0, 20)
    resourcesFrame.Position = UDim2.new(0, 5, 0, 110)
    resourcesFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    resourcesFrame.BorderSizePixel = 1
    resourcesFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    resourcesFrame.Parent = hudFrame

    -- Ícone de recursos
    local resourcesIcon = Instance.new("TextLabel")
    resourcesIcon.Size = UDim2.new(0, 20, 1, 0)
    resourcesIcon.Position = UDim2.new(0, 0, 0, 0)
    resourcesIcon.BackgroundTransparency = 1
    resourcesIcon.Text = "💎"
    resourcesIcon.TextColor3 = Color3.new(1, 1, 1)
    resourcesIcon.TextScaled = true
    resourcesIcon.Font = Enum.Font.SourceSansBold
    resourcesIcon.Parent = resourcesFrame

    -- Texto dos recursos carregados
    resourcesCarriedText = Instance.new("TextLabel")
    resourcesCarriedText.Size = UDim2.new(1, -25, 1, 0)
    resourcesCarriedText.Position = UDim2.new(0, 25, 0, 0)
    resourcesCarriedText.BackgroundTransparency = 1
    resourcesCarriedText.Text = "Recursos: 0"
    resourcesCarriedText.TextColor3 = Color3.new(1, 1, 1)
    resourcesCarriedText.TextScaled = true
    resourcesCarriedText.Font = Enum.Font.SourceSansBold
    resourcesCarriedText.TextXAlignment = Enum.TextXAlignment.Left
    resourcesCarriedText.Parent = resourcesFrame

    -- === INDICADOR DE STATUS ===
    statusIndicator = Instance.new("Frame")
    statusIndicator.Size = UDim2.new(1, -10, 0, 20)
    statusIndicator.Position = UDim2.new(0, 5, 0, 135)
    statusIndicator.BackgroundColor3 = Color3.new(0, 0.5, 0)
    statusIndicator.BorderSizePixel = 1
    statusIndicator.BorderColor3 = Color3.new(1, 1, 1)
    statusIndicator.Parent = hudFrame

    local statusText = Instance.new("TextLabel")
    statusText.Name = "StatusText"
    statusText.Size = UDim2.new(1, 0, 1, 0)
    statusText.BackgroundTransparency = 1
    statusText.Text = "✅ Normal"
    statusText.TextColor3 = Color3.new(1, 1, 1)
    statusText.TextScaled = true
    statusText.Font = Enum.Font.SourceSansBold
    statusText.Parent = statusIndicator

    -- === INDICADOR DE VELOCIDADE ===
    speedIndicator = Instance.new("Frame")
    speedIndicator.Size = UDim2.new(1, -10, 0, 20)
    speedIndicator.Position = UDim2.new(0, 5, 0, 160)
    speedIndicator.BackgroundColor3 = Color3.new(0, 0.7, 0)
    speedIndicator.BorderSizePixel = 1
    speedIndicator.BorderColor3 = Color3.new(1, 1, 1)
    speedIndicator.Visible = false
    speedIndicator.Parent = hudFrame

    local speedText = Instance.new("TextLabel")
    speedText.Name = "SpeedText"
    speedText.Size = UDim2.new(1, 0, 1, 0)
    speedText.BackgroundTransparency = 1
    speedText.Text = "🐌 Lento (Carregando Recursos)"
    speedText.TextColor3 = Color3.new(1, 1, 1)
    speedText.TextScaled = true
    speedText.Font = Enum.Font.SourceSansBold
    speedText.Parent = speedIndicator

    -- === SISTEMA DE DASH ===
    -- Frame do dash (posicionado no canto inferior direito)
    dashCooldownFrame = Instance.new("Frame")
    dashCooldownFrame.Size = UDim2.new(0, 120, 0, 60)
    dashCooldownFrame.Position = UDim2.new(1, -130, 1, -70)
    dashCooldownFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    dashCooldownFrame.BackgroundTransparency = 0.3
    dashCooldownFrame.BorderSizePixel = 2
    dashCooldownFrame.BorderColor3 = Color3.new(0, 1, 0)
    dashCooldownFrame.Parent = screenGui

    -- Cantos arredondados para o dash
    local dashCorner = Instance.new("UICorner")
    dashCorner.CornerRadius = UDim.new(0, 8)
    dashCorner.Parent = dashCooldownFrame

    -- Título do dash
    local dashTitle = Instance.new("TextLabel")
    dashTitle.Size = UDim2.new(1, 0, 0, 20)
    dashTitle.Position = UDim2.new(0, 0, 0, 0)
    dashTitle.BackgroundTransparency = 1
    dashTitle.Text = "⚡ DASH"
    dashTitle.TextColor3 = Color3.new(1, 1, 1)
    dashTitle.TextScaled = true
    dashTitle.Font = Enum.Font.SourceSansBold
    dashTitle.Parent = dashCooldownFrame

    -- Background da barra de cooldown
    local dashBarBG = Instance.new("Frame")
    dashBarBG.Size = UDim2.new(1, -10, 0, 15)
    dashBarBG.Position = UDim2.new(0, 5, 0, 25)
    dashBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    dashBarBG.BorderSizePixel = 1
    dashBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    dashBarBG.Parent = dashCooldownFrame

    -- Barra de cooldown do dash
    dashCooldownBar = Instance.new("Frame")
    dashCooldownBar.Size = UDim2.new(0, 0, 1, 0)
    dashCooldownBar.Position = UDim2.new(0, 0, 0, 0)
    dashCooldownBar.BackgroundColor3 = Color3.new(1, 1, 0)
    dashCooldownBar.BorderSizePixel = 0
    dashCooldownBar.Parent = dashBarBG

    -- Texto do cooldown
    dashCooldownText = Instance.new("TextLabel")
    dashCooldownText.Size = UDim2.new(1, 0, 0, 20)
    dashCooldownText.Position = UDim2.new(0, 0, 0, 40)
    dashCooldownText.BackgroundTransparency = 1
    dashCooldownText.Text = "Pronto!"
    dashCooldownText.TextColor3 = Color3.new(0, 1, 0)
    dashCooldownText.TextScaled = true
    dashCooldownText.Font = Enum.Font.SourceSansBold
    dashCooldownText.Parent = dashCooldownFrame

    -- === STATUS DA BARREIRA ===
    barrierStatusFrame = Instance.new("Frame")
    barrierStatusFrame.Size = UDim2.new(0, 200, 0, 30)
    barrierStatusFrame.Position = UDim2.new(0.5, -100, 0, 220)
    barrierStatusFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    barrierStatusFrame.BackgroundTransparency = 0.3
    barrierStatusFrame.BorderSizePixel = 2
    barrierStatusFrame.BorderColor3 = Color3.new(1, 1, 1)
    barrierStatusFrame.Visible = false
    barrierStatusFrame.Parent = screenGui

    -- Cantos arredondados para status da barreira
    local barrierCorner = Instance.new("UICorner")
    barrierCorner.CornerRadius = UDim.new(0, 8)
    barrierCorner.Parent = barrierStatusFrame

    local barrierStatusText = Instance.new("TextLabel")
    barrierStatusText.Name = "BarrierStatusText"
    barrierStatusText.Size = UDim2.new(1, 0, 1, 0)
    barrierStatusText.BackgroundTransparency = 1
    barrierStatusText.Text = "🛡️ Na Barreira Própria - Curando"
    barrierStatusText.TextColor3 = Color3.new(1, 1, 1)
    barrierStatusText.TextScaled = true
    barrierStatusText.Font = Enum.Font.SourceSansBold
    barrierStatusText.Parent = barrierStatusFrame
end

-- Função para atualizar a vida
local function updateHealth()
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    local health = math.floor(humanoid.Health)
    local maxHealth = math.floor(humanoid.MaxHealth)
    local healthRatio = health / maxHealth
    
    -- Atualiza texto
    healthText.Text = health .. "/" .. maxHealth
    
    -- Atualiza barra com animação
    local targetSize = UDim2.new(healthRatio, 0, 1, 0)
    local tween = TweenService:Create(healthBar, TweenInfo.new(0.3), {Size = targetSize})
    tween:Play()
    
    -- Muda cor baseada na vida
    if healthRatio > 0.6 then
        healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    elseif healthRatio > 0.3 then
        healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end
end

-- Função para atualizar recursos carregados
local function updateResourcesCarried()
    local resourceCount = 0
    local isCarrying = false

    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        local humanoidRootPart = player.Character.HumanoidRootPart
        for _, child in ipairs(humanoidRootPart:GetChildren()) do
            if child.Name == "CarryingResource" then
                resourceCount = resourceCount + 1
                isCarrying = true
            end
        end
    end

    isCarryingResources = isCarrying
    resourcesCarried = resourceCount

    if resourcesCarriedText then
        resourcesCarriedText.Text = "Recursos: " .. resourceCount
        if isCarrying then
            resourcesCarriedText.TextColor3 = Color3.new(1, 1, 0) -- Amarelo quando carregando
        else
            resourcesCarriedText.TextColor3 = Color3.new(1, 1, 1) -- Branco normal
        end
    end

    -- Atualiza indicador de velocidade
    if speedIndicator then
        speedIndicator.Visible = isCarrying
        if isCarrying then
            speedIndicator.BackgroundColor3 = Color3.new(1, 0.5, 0) -- Laranja
        end
    end
end

-- Função para atualizar status da barreira
local function updateBarrierStatus()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end

    local playerPosition = player.Character.HumanoidRootPart.Position
    local inBarrier = false
    local inOwnBarrier = false
    local barrierOwner = nil

    -- Verifica todas as bases
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local barrier = base:FindFirstChild("Barrier")
            if barrier then
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2

                if distance <= barrierRadius then
                    inBarrier = true
                    local owner = base:FindFirstChild("Owner")
                    local partner = base:FindFirstChild("Partner")

                    if (owner and owner.Value == player) or (partner and partner.Value == player) then
                        inOwnBarrier = true
                        barrierOwner = "própria"
                    else
                        inOwnBarrier = false
                        if owner and owner.Value then
                            barrierOwner = owner.Value.Name
                        else
                            barrierOwner = "inimiga"
                        end
                    end
                    break
                end
            end
        end
    end

    isInBarrier = inBarrier
    isInOwnBarrier = inOwnBarrier

    -- Atualiza UI da barreira
    if barrierStatusFrame then
        barrierStatusFrame.Visible = inBarrier
        if inBarrier then
            local barrierStatusText = barrierStatusFrame:FindFirstChild("BarrierStatusText")
            if barrierStatusText then
                if inOwnBarrier then
                    barrierStatusText.Text = "🛡️ Na Barreira Própria - Curando"
                    barrierStatusFrame.BorderColor3 = Color3.new(0, 1, 0) -- Verde
                else
                    barrierStatusText.Text = "⚠️ Na Barreira de " .. barrierOwner .. " - Tomando Dano!"
                    barrierStatusFrame.BorderColor3 = Color3.new(1, 0, 0) -- Vermelho
                end
            end
        end
    end
end

-- Função para atualizar dash cooldown
local function updateDashCooldown()
    if dashCooldownActive then
        dashCooldownTime = dashCooldownTime - (1/60) -- Reduz por frame

        if dashCooldownTime <= 0 then
            dashCooldownActive = false
            dashCooldownTime = 0
        end

        -- Atualiza barra de cooldown
        local cooldownRatio = dashCooldownTime / 5 -- 5 segundos de cooldown
        if dashCooldownBar then
            dashCooldownBar.Size = UDim2.new(cooldownRatio, 0, 1, 0)
        end

        -- Atualiza texto
        if dashCooldownText then
            dashCooldownText.Text = string.format("%.1fs", dashCooldownTime)
            dashCooldownText.TextColor3 = Color3.new(1, 0, 0) -- Vermelho durante cooldown
        end

        -- Atualiza cor da borda
        if dashCooldownFrame then
            dashCooldownFrame.BorderColor3 = Color3.new(1, 0, 0) -- Vermelho durante cooldown
        end
    else
        -- Dash disponível
        if dashCooldownText then
            dashCooldownText.Text = "Pronto!"
            dashCooldownText.TextColor3 = Color3.new(0, 1, 0) -- Verde quando pronto
        end

        if dashCooldownFrame then
            dashCooldownFrame.BorderColor3 = Color3.new(0, 1, 0) -- Verde quando pronto
        end

        if dashCooldownBar then
            dashCooldownBar.Size = UDim2.new(0, 0, 1, 0) -- Barra vazia quando pronto
        end
    end
end

-- Função para ativar dash
local function activateDash()
    if not dashCooldownActive and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        dashCooldownActive = true
        dashCooldownTime = 5 -- 5 segundos de cooldown

        -- Efeito do dash
        local humanoidRootPart = player.Character.HumanoidRootPart
        local bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000)
        bodyVelocity.Velocity = humanoidRootPart.CFrame.LookVector * 250 -- Velocidade do dash aumentada 5x (50 * 5)
        bodyVelocity.Parent = humanoidRootPart

        -- Remove o efeito após 0.3 segundos
        spawn(function()
            wait(0.3)
            if bodyVelocity and bodyVelocity.Parent then
                bodyVelocity:Destroy()
            end
        end)

        print("⚡ Dash ativado!")
    end
end

-- Função para atualizar informações da base
local function updateBaseInfoDisplay(baseName, baseSize, buildingMaterials)
    if baseName then
        -- Tem base
        local maxBaseSize = 500
        local baseRatio = baseSize / maxBaseSize
        
        -- Atualiza texto da base
        baseText.Text = baseName .. ": " .. baseSize .. "/" .. maxBaseSize
        
        -- Atualiza barra da base com animação
        local targetSize = UDim2.new(baseRatio, 0, 1, 0)
        local tween = TweenService:Create(baseBar, TweenInfo.new(0.5), {Size = targetSize})
        tween:Play()
        
        -- Muda cor baseada no tamanho
        if baseRatio > 0.6 then
            baseBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
        elseif baseRatio > 0.3 then
            baseBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
        else
            baseBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
        end
        
        -- Atualiza materiais
        materialsText.Text = "Materiais: " .. buildingMaterials
    else
        -- Não tem base
        baseText.Text = "Nenhuma Base"
        baseBar.Size = UDim2.new(0, 0, 1, 0)
        materialsText.Text = "Materiais: 0"
    end
end

-- Função para verificar base do jogador
local function checkPlayerBase()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
                
                if baseSizeValue and buildingMaterialsValue then
                    updateBaseInfo(base.Name, baseSizeValue.Value, buildingMaterialsValue.Value)
                    return
                end
            end
        end
    end
    
    -- Não encontrou base
    updateBaseInfo(nil, 0, 0)
end

-- Inicializa o HUD
local function initializeHUD()
    createHUD()

    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end

        updateHealth()
        checkPlayerBase()
        updateResourcesCarried()
        updateBarrierStatus()
        updateDashCooldown()
    end)

    -- Conecta tecla de dash (Shift esquerdo)
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.LeftShift then
            activateDash()
        end
    end)
end

-- Conecta evento de atualização da base
updateBaseInfo.OnClientEvent:Connect(function(baseName, baseSize, buildingMaterials)
    updateBaseInfoDisplay(baseName, baseSize, buildingMaterials)
end)

-- Inicializa quando o jogador spawna
if player.Character then
    initializeHUD()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeHUD()
end)

print("MainHUD carregado com sucesso!")