-- MinimapRadar.lua
-- Mini-mapa e radar para navegação e consciência situacional

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Variáveis da UI
local screenGui = nil
local minimapFrame = nil
local radarFrame = nil
local playerDot = nil
local isMinimapVisible = true
local minimapScale = 0.1 -- Escala do mapa (1 stud = 0.1 pixel)
local radarRange = 200 -- Alcance do radar em studs

-- Objetos rastreados
local trackedObjects = {
    bases = {},
    resources = {},
    players = {},
    barriers = {}
}

-- Cores para diferentes elementos
local COLORS = {
    PLAYER = Color3.new(0, 1, 0),        -- Verde para o jogador
    TEAMMATE = Color3.new(0, 0.8, 1),    -- Azul claro para companheiros
    ENEMY = Color3.new(1, 0.2, 0.2),     -- Vermelho para inimigos
    OWN_BASE = Color3.new(0, 1, 0),      -- Verde para base própria
    ENEMY_BASE = Color3.new(1, 0, 0),    -- Vermelho para bases inimigas
    NEUTRAL_BASE = Color3.new(0.7, 0.7, 0.7), -- Cinza para bases neutras
    RESOURCE = Color3.new(1, 1, 0),      -- Amarelo para recursos
    BARRIER = Color3.new(0, 0.8, 1)      -- Azul para barreiras
}

-- Função para criar o mini-mapa
local function createMinimap()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("MinimapRadar")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MinimapRadar"
    screenGui.Parent = playerGui
    
    -- Frame principal do mini-mapa
    minimapFrame = Instance.new("Frame")
    minimapFrame.Name = "MinimapFrame"
    minimapFrame.Size = UDim2.new(0, 200, 0, 200)
    minimapFrame.Position = UDim2.new(1, -210, 0, 10)
    minimapFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    minimapFrame.BackgroundTransparency = 0.2
    minimapFrame.BorderSizePixel = 2
    minimapFrame.BorderColor3 = Color3.new(1, 1, 1)
    minimapFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = minimapFrame
    
    -- Título do mini-mapa
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 25)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    titleLabel.BackgroundTransparency = 0.3
    titleLabel.Text = "🗺️ MINI-MAPA"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = minimapFrame
    
    -- Cantos arredondados para o título
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 8)
    titleCorner.Parent = titleLabel
    
    -- Área do radar
    radarFrame = Instance.new("Frame")
    radarFrame.Name = "RadarFrame"
    radarFrame.Size = UDim2.new(1, -10, 1, -35)
    radarFrame.Position = UDim2.new(0, 5, 0, 30)
    radarFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    radarFrame.BackgroundTransparency = 0.1
    radarFrame.BorderSizePixel = 1
    radarFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    radarFrame.ClipsDescendants = true
    radarFrame.Parent = minimapFrame
    
    -- Cantos arredondados para o radar
    local radarCorner = Instance.new("UICorner")
    radarCorner.CornerRadius = UDim.new(0, 5)
    radarCorner.Parent = radarFrame
    
    -- Círculos de alcance do radar
    for i = 1, 3 do
        local circle = Instance.new("Frame")
        circle.Name = "RangeCircle" .. i
        local radius = (radarRange / 3) * i * minimapScale
        circle.Size = UDim2.new(0, radius * 2, 0, radius * 2)
        circle.Position = UDim2.new(0.5, -radius, 0.5, -radius)
        circle.BackgroundTransparency = 1
        circle.BorderSizePixel = 1
        circle.BorderColor3 = Color3.new(0.2, 0.2, 0.2)
        circle.Parent = radarFrame
        
        -- Torna circular
        local circleCorner = Instance.new("UICorner")
        circleCorner.CornerRadius = UDim.new(0.5, 0)
        circleCorner.Parent = circle
    end
    
    -- Ponto do jogador (centro do radar)
    playerDot = Instance.new("Frame")
    playerDot.Name = "PlayerDot"
    playerDot.Size = UDim2.new(0, 8, 0, 8)
    playerDot.Position = UDim2.new(0.5, -4, 0.5, -4)
    playerDot.BackgroundColor3 = COLORS.PLAYER
    playerDot.BorderSizePixel = 1
    playerDot.BorderColor3 = Color3.new(1, 1, 1)
    playerDot.Parent = radarFrame
    
    -- Torna circular
    local playerCorner = Instance.new("UICorner")
    playerCorner.CornerRadius = UDim.new(0.5, 0)
    playerCorner.Parent = playerDot
    
    -- Botão para alternar visibilidade
    local toggleButton = Instance.new("TextButton")
    toggleButton.Size = UDim2.new(0, 30, 0, 30)
    toggleButton.Position = UDim2.new(1, -35, 0, -5)
    toggleButton.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    toggleButton.BorderSizePixel = 1
    toggleButton.BorderColor3 = Color3.new(1, 1, 1)
    toggleButton.Text = "👁️"
    toggleButton.TextColor3 = Color3.new(1, 1, 1)
    toggleButton.TextScaled = true
    toggleButton.Font = Enum.Font.SourceSansBold
    toggleButton.Parent = minimapFrame
    
    -- Cantos arredondados para o botão
    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0.5, 0)
    buttonCorner.Parent = toggleButton
    
    -- Conecta evento do botão
    toggleButton.MouseButton1Click:Connect(function()
        isMinimapVisible = not isMinimapVisible
        minimapFrame.Visible = isMinimapVisible
        if isMinimapVisible then
            toggleButton.Text = "👁️"
        else
            toggleButton.Text = "🚫"
        end
    end)
    
    -- Instruções (aparecem ao passar o mouse)
    local instructionLabel = Instance.new("TextLabel")
    instructionLabel.Size = UDim2.new(1, 0, 0, 20)
    instructionLabel.Position = UDim2.new(0, 0, 1, 5)
    instructionLabel.BackgroundTransparency = 1
    instructionLabel.Text = "M - Alternar | Verde: Você | Azul: Equipe | Vermelho: Inimigos"
    instructionLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)
    instructionLabel.TextScaled = true
    instructionLabel.Font = Enum.Font.SourceSans
    instructionLabel.Visible = false
    instructionLabel.Parent = minimapFrame
    
    -- Mostra instruções ao passar o mouse
    minimapFrame.MouseEnter:Connect(function()
        instructionLabel.Visible = true
    end)
    
    minimapFrame.MouseLeave:Connect(function()
        instructionLabel.Visible = false
    end)
end

-- Função para converter posição do mundo para posição do radar
local function worldToRadar(worldPosition, playerPosition)
    local relativePos = worldPosition - playerPosition
    local radarX = (relativePos.X * minimapScale) + (radarFrame.AbsoluteSize.X / 2)
    local radarZ = (-relativePos.Z * minimapScale) + (radarFrame.AbsoluteSize.Y / 2) -- Z invertido
    
    return Vector2.new(radarX, radarZ)
end

-- Função para criar um ponto no radar
local function createRadarDot(name, color, size)
    local dot = Instance.new("Frame")
    dot.Name = name
    dot.Size = UDim2.new(0, size or 6, 0, size or 6)
    dot.BackgroundColor3 = color
    dot.BorderSizePixel = 1
    dot.BorderColor3 = Color3.new(1, 1, 1)
    dot.Parent = radarFrame
    
    -- Torna circular
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0.5, 0)
    corner.Parent = dot
    
    return dot
end

-- Função para atualizar posições no radar
local function updateRadar()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    -- Limpa pontos antigos
    for _, child in ipairs(radarFrame:GetChildren()) do
        if child.Name:match("Dot_") or child.Name:match("Base_") or child.Name:match("Resource_") then
            child:Destroy()
        end
    end
    
    -- Atualiza bases
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local basePlatform = base:FindFirstChild("BasePlatform")
            if basePlatform then
                local distance = (basePlatform.Position - playerPosition).Magnitude
                if distance <= radarRange then
                    local radarPos = worldToRadar(basePlatform.Position, playerPosition)
                    
                    -- Determina cor da base
                    local baseColor = COLORS.NEUTRAL_BASE
                    local owner = base:FindFirstChild("Owner")
                    local partner = base:FindFirstChild("Partner")
                    
                    if (owner and owner.Value == player) or (partner and partner.Value == player) then
                        baseColor = COLORS.OWN_BASE
                    elseif owner and owner.Value then
                        baseColor = COLORS.ENEMY_BASE
                    end
                    
                    local baseDot = createRadarDot("Base_" .. base.Name, baseColor, 10)
                    baseDot.Position = UDim2.new(0, radarPos.X - 5, 0, radarPos.Y - 5)
                    
                    -- Adiciona indicador de barreira
                    local barrier = base:FindFirstChild("Barrier")
                    if barrier then
                        local barrierRadius = (barrier.Size.X / 2) * minimapScale
                        local barrierCircle = Instance.new("Frame")
                        barrierCircle.Name = "Barrier_" .. base.Name
                        barrierCircle.Size = UDim2.new(0, barrierRadius * 2, 0, barrierRadius * 2)
                        barrierCircle.Position = UDim2.new(0, radarPos.X - barrierRadius, 0, radarPos.Y - barrierRadius)
                        barrierCircle.BackgroundTransparency = 0.8
                        barrierCircle.BackgroundColor3 = baseColor
                        barrierCircle.BorderSizePixel = 1
                        barrierCircle.BorderColor3 = baseColor
                        barrierCircle.Parent = radarFrame
                        
                        local barrierCorner = Instance.new("UICorner")
                        barrierCorner.CornerRadius = UDim.new(0.5, 0)
                        barrierCorner.Parent = barrierCircle
                    end
                end
            end
        end
    end
    
    -- Atualiza outros jogadores
    for _, otherPlayer in ipairs(Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character and otherPlayer.Character:FindFirstChild("HumanoidRootPart") then
            local distance = (otherPlayer.Character.HumanoidRootPart.Position - playerPosition).Magnitude
            if distance <= radarRange then
                local radarPos = worldToRadar(otherPlayer.Character.HumanoidRootPart.Position, playerPosition)
                
                -- Determina se é companheiro ou inimigo
                local playerColor = COLORS.ENEMY
                local isTeammate = false
                
                -- Verifica se é da mesma equipe
                for _, base in ipairs(workspace:GetChildren()) do
                    if base.Name:match("Base_") then
                        local owner = base:FindFirstChild("Owner")
                        local partner = base:FindFirstChild("Partner")
                        
                        local playerInThisBase = (owner and owner.Value == player) or (partner and partner.Value == player)
                        local otherInThisBase = (owner and owner.Value == otherPlayer) or (partner and partner.Value == otherPlayer)
                        
                        if playerInThisBase and otherInThisBase then
                            isTeammate = true
                            playerColor = COLORS.TEAMMATE
                            break
                        end
                    end
                end
                
                local playerDot = createRadarDot("Dot_" .. otherPlayer.Name, playerColor, 6)
                playerDot.Position = UDim2.new(0, radarPos.X - 3, 0, radarPos.Y - 3)
                
                -- Adiciona nome do jogador
                local nameLabel = Instance.new("TextLabel")
                nameLabel.Size = UDim2.new(0, 60, 0, 12)
                nameLabel.Position = UDim2.new(0, radarPos.X - 30, 0, radarPos.Y - 15)
                nameLabel.BackgroundTransparency = 1
                nameLabel.Text = otherPlayer.Name
                nameLabel.TextColor3 = playerColor
                nameLabel.TextScaled = true
                nameLabel.Font = Enum.Font.SourceSans
                nameLabel.Parent = radarFrame
            end
        end
    end
    
    -- Atualiza recursos próximos
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj.Name:match("Resource_") and obj:FindFirstChild("OriginalSize") then
            local distance = (obj.Position - playerPosition).Magnitude
            if distance <= radarRange / 2 then -- Recursos aparecem em menor distância
                local radarPos = worldToRadar(obj.Position, playerPosition)
                local resourceDot = createRadarDot("Resource_" .. obj.Name, COLORS.RESOURCE, 4)
                resourceDot.Position = UDim2.new(0, radarPos.X - 2, 0, radarPos.Y - 2)
            end
        end
    end
end

-- Inicialização
local function initializeMinimap()
    createMinimap()
    
    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        if isMinimapVisible and minimapFrame and minimapFrame.Visible then
            updateRadar()
        end
    end)
    
    -- Conecta tecla M para alternar
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.M then
            isMinimapVisible = not isMinimapVisible
            if minimapFrame then
                minimapFrame.Visible = isMinimapVisible
            end
        end
    end)
end

-- Inicializa quando o jogador spawna
if player.Character then
    initializeMinimap()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeMinimap()
end)

print("MinimapRadar carregado com sucesso!")
