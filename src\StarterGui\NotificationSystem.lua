-- NotificationSystem.lua
-- Sistema avançado de notificações para eventos do jogo

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Variáveis da UI
local screenGui = nil
local notificationQueue = {}
local activeNotifications = {}
local maxNotifications = 5

-- Tipos de notificação com configurações
local NOTIFICATION_TYPES = {
    SUCCESS = {
        color = Color3.new(0, 0.8, 0),
        icon = "✅",
        sound = "rbxasset://sounds/electronicpingblip.wav",
        duration = 3
    },
    WARNING = {
        color = Color3.new(1, 0.8, 0),
        icon = "⚠️",
        sound = "rbxasset://sounds/button-09.wav",
        duration = 4
    },
    ERROR = {
        color = Color3.new(0.8, 0, 0),
        icon = "❌",
        sound = "rbxasset://sounds/button-10.wav",
        duration = 5
    },
    INFO = {
        color = Color3.new(0, 0.6, 1),
        icon = "ℹ️",
        sound = "rbxasset://sounds/button-3.wav",
        duration = 3
    },
    ACHIEVEMENT = {
        color = Color3.new(1, 0.8, 0),
        icon = "🏆",
        sound = "rbxasset://sounds/victory.wav",
        duration = 6
    },
    COMBAT = {
        color = Color3.new(1, 0.2, 0.2),
        icon = "⚔️",
        sound = "rbxasset://sounds/impact_generic.wav",
        duration = 4
    },
    RESOURCE = {
        color = Color3.new(0.2, 1, 0.2),
        icon = "💎",
        sound = "rbxasset://sounds/pickup_01.wav",
        duration = 3
    },
    BASE = {
        color = Color3.new(0.2, 0.8, 1),
        icon = "🏠",
        sound = "rbxasset://sounds/button-4.wav",
        duration = 4
    }
}

-- Função para criar a interface de notificações
local function createNotificationUI()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("NotificationSystem")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "NotificationSystem"
    screenGui.Parent = playerGui
end

-- Função para criar uma notificação
local function createNotification(message, notificationType, customDuration)
    local config = NOTIFICATION_TYPES[notificationType] or NOTIFICATION_TYPES.INFO
    local duration = customDuration or config.duration
    
    -- Frame da notificação
    local notificationFrame = Instance.new("Frame")
    notificationFrame.Size = UDim2.new(0, 350, 0, 80)
    notificationFrame.Position = UDim2.new(1, 10, 0, 10) -- Começa fora da tela
    notificationFrame.BackgroundColor3 = config.color
    notificationFrame.BackgroundTransparency = 0.1
    notificationFrame.BorderSizePixel = 2
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = notificationFrame
    
    -- Ícone
    local iconLabel = Instance.new("TextLabel")
    iconLabel.Size = UDim2.new(0, 60, 1, 0)
    iconLabel.Position = UDim2.new(0, 0, 0, 0)
    iconLabel.BackgroundTransparency = 1
    iconLabel.Text = config.icon
    iconLabel.TextColor3 = Color3.new(1, 1, 1)
    iconLabel.TextScaled = true
    iconLabel.Font = Enum.Font.SourceSansBold
    iconLabel.Parent = notificationFrame
    
    -- Texto da mensagem
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, -70, 1, -10)
    messageLabel.Position = UDim2.new(0, 65, 0, 5)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = message
    messageLabel.TextColor3 = Color3.new(1, 1, 1)
    messageLabel.TextScaled = true
    messageLabel.TextWrapped = true
    messageLabel.TextXAlignment = Enum.TextXAlignment.Left
    messageLabel.Font = Enum.Font.SourceSans
    messageLabel.Parent = notificationFrame
    
    -- Barra de progresso (tempo restante)
    local progressBar = Instance.new("Frame")
    progressBar.Size = UDim2.new(1, 0, 0, 3)
    progressBar.Position = UDim2.new(0, 0, 1, -3)
    progressBar.BackgroundColor3 = Color3.new(1, 1, 1)
    progressBar.BackgroundTransparency = 0.3
    progressBar.BorderSizePixel = 0
    progressBar.Parent = notificationFrame
    
    -- Animação de entrada
    local targetPosition = UDim2.new(1, -360, 0, 10 + (#activeNotifications * 90))
    local enterTween = TweenService:Create(
        notificationFrame,
        TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = targetPosition}
    )
    enterTween:Play()
    
    -- Adiciona à lista de notificações ativas
    table.insert(activeNotifications, {
        frame = notificationFrame,
        duration = duration,
        timeLeft = duration,
        progressBar = progressBar
    })
    
    -- Toca som se disponível
    if config.sound then
        local sound = Instance.new("Sound")
        sound.SoundId = config.sound
        sound.Volume = 0.5
        sound.Parent = SoundService
        sound:Play()
        
        sound.Ended:Connect(function()
            sound:Destroy()
        end)
    end
    
    -- Reposiciona outras notificações
    repositionNotifications()
    
    return notificationFrame
end

-- Função para reposicionar notificações
local function repositionNotifications()
    for i, notification in ipairs(activeNotifications) do
        local targetPosition = UDim2.new(1, -360, 0, 10 + ((i-1) * 90))
        local repositionTween = TweenService:Create(
            notification.frame,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Position = targetPosition}
        )
        repositionTween:Play()
    end
end

-- Função para remover uma notificação
local function removeNotification(index)
    if activeNotifications[index] then
        local notification = activeNotifications[index]
        
        -- Animação de saída
        local exitTween = TweenService:Create(
            notification.frame,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
            {
                Position = UDim2.new(1, 10, notification.frame.Position.Y.Scale, notification.frame.Position.Y.Offset),
                BackgroundTransparency = 1
            }
        )
        
        exitTween:Play()
        exitTween.Completed:Connect(function()
            notification.frame:Destroy()
        end)
        
        -- Remove da lista
        table.remove(activeNotifications, index)
        
        -- Reposiciona outras notificações
        repositionNotifications()
    end
end

-- Função para atualizar notificações
local function updateNotifications()
    for i = #activeNotifications, 1, -1 do
        local notification = activeNotifications[i]
        notification.timeLeft = notification.timeLeft - (1/60)
        
        -- Atualiza barra de progresso
        local progress = notification.timeLeft / notification.duration
        notification.progressBar.Size = UDim2.new(progress, 0, 0, 3)
        
        -- Remove se o tempo acabou
        if notification.timeLeft <= 0 then
            removeNotification(i)
        end
    end
end

-- Função pública para mostrar notificação
local function showNotification(message, notificationType, duration)
    -- Limita o número de notificações
    while #activeNotifications >= maxNotifications do
        removeNotification(1)
    end
    
    createNotification(message, notificationType or "INFO", duration)
end

-- Funções específicas para diferentes tipos de eventos
local function showResourceCollected(resourceType, amount)
    showNotification(
        "Coletou " .. amount .. "x " .. resourceType .. "!",
        "RESOURCE"
    )
end

local function showBaseAttacked(attacker, damage)
    showNotification(
        attacker .. " atacou sua base! (-" .. damage .. " tamanho)",
        "COMBAT"
    )
end

local function showPlayerKilled(victim)
    showNotification(
        "Eliminou " .. victim .. "!",
        "COMBAT"
    )
end

local function showPlayerDied(killer)
    showNotification(
        "Foi eliminado por " .. killer .. "!",
        "ERROR"
    )
end

local function showBaseExpanded(newSize)
    showNotification(
        "Base expandida! Novo tamanho: " .. newSize,
        "BASE"
    )
end

local function showAchievementUnlocked(achievementName)
    showNotification(
        "Conquista desbloqueada: " .. achievementName .. "!",
        "ACHIEVEMENT"
    )
end

local function showInviteReceived(playerName)
    showNotification(
        playerName .. " convidou você para a equipe!",
        "INFO",
        8 -- Mais tempo para convites
    )
end

local function showConstructionBuilt(buildingType, cost)
    showNotification(
        buildingType .. " construído! (-" .. cost .. " materiais)",
        "SUCCESS"
    )
end

-- Inicialização
local function initializeNotificationSystem()
    createNotificationUI()
    
    -- Loop de atualização
    game:GetService("RunService").Heartbeat:Connect(updateNotifications)
    
    -- Conecta eventos do jogo (se existirem)
    if remoteEvents:FindFirstChild("ShowNotification") then
        remoteEvents.ShowNotification.OnClientEvent:Connect(function(message, type, duration)
            showNotification(message, type, duration)
        end)
    end
    
    -- Exemplo de notificação de boas-vindas
    task.wait(2)
    showNotification("Sistema de notificações carregado!", "SUCCESS")
end

-- Exporta funções para uso global
_G.NotificationSystem = {
    show = showNotification,
    resourceCollected = showResourceCollected,
    baseAttacked = showBaseAttacked,
    playerKilled = showPlayerKilled,
    playerDied = showPlayerDied,
    baseExpanded = showBaseExpanded,
    achievementUnlocked = showAchievementUnlocked,
    inviteReceived = showInviteReceived,
    constructionBuilt = showConstructionBuilt
}

-- Inicializa quando o jogador spawna
if player.Character then
    initializeNotificationSystem()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeNotificationSystem()
end)

print("NotificationSystem carregado com sucesso!")
