-- RespawnUI.lua
-- Sistema de respawn com timer visual e informações detalhadas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Variáveis da UI
local screenGui = nil
local respawnFrame = nil
local timerBar = nil
local timerText = nil
local deathInfoText = nil
local respawnConnection = nil

-- Variáveis de estado
local respawnTime = 0
local maxRespawnTime = 10
local isRespawning = false

-- Função para criar a interface de respawn
local function createRespawnUI()
    local playerGui = player:WaitF<PERSON><PERSON>hild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("RespawnUI")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "RespawnUI"
    screenGui.Parent = playerGui
    
    -- Frame principal de respawn (tela cheia com fundo escuro)
    respawnFrame = Instance.new("Frame")
    respawnFrame.Name = "RespawnFrame"
    respawnFrame.Size = UDim2.new(1, 0, 1, 0)
    respawnFrame.Position = UDim2.new(0, 0, 0, 0)
    respawnFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    respawnFrame.BackgroundTransparency = 0.3
    respawnFrame.BorderSizePixel = 0
    respawnFrame.Visible = false
    respawnFrame.Parent = screenGui
    
    -- Frame central com informações
    local centralFrame = Instance.new("Frame")
    centralFrame.Size = UDim2.new(0, 400, 0, 300)
    centralFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
    centralFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    centralFrame.BackgroundTransparency = 0.1
    centralFrame.BorderSizePixel = 3
    centralFrame.BorderColor3 = Color3.new(1, 0, 0)
    centralFrame.Parent = respawnFrame
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = centralFrame
    
    -- Título "VOCÊ MORREU"
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 60)
    titleLabel.Position = UDim2.new(0, 0, 0, 10)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "💀 VOCÊ MORREU!"
    titleLabel.TextColor3 = Color3.new(1, 0, 0)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = centralFrame
    
    -- Informações sobre a morte
    deathInfoText = Instance.new("TextLabel")
    deathInfoText.Size = UDim2.new(1, -20, 0, 80)
    deathInfoText.Position = UDim2.new(0, 10, 0, 70)
    deathInfoText.BackgroundTransparency = 1
    deathInfoText.Text = "Causa: Desconhecida\nBase perdeu 20 de tamanho\nRecursos carregados foram perdidos"
    deathInfoText.TextColor3 = Color3.new(1, 1, 1)
    deathInfoText.TextScaled = true
    deathInfoText.TextWrapped = true
    deathInfoText.TextXAlignment = Enum.TextXAlignment.Center
    deathInfoText.Font = Enum.Font.SourceSans
    deathInfoText.Parent = centralFrame
    
    -- Label do timer
    local timerLabel = Instance.new("TextLabel")
    timerLabel.Size = UDim2.new(1, 0, 0, 30)
    timerLabel.Position = UDim2.new(0, 0, 0, 160)
    timerLabel.BackgroundTransparency = 1
    timerLabel.Text = "⏰ RESPAWN EM:"
    timerLabel.TextColor3 = Color3.new(1, 1, 1)
    timerLabel.TextScaled = true
    timerLabel.Font = Enum.Font.SourceSansBold
    timerLabel.Parent = centralFrame
    
    -- Background da barra de timer
    local timerBarBG = Instance.new("Frame")
    timerBarBG.Size = UDim2.new(1, -40, 0, 30)
    timerBarBG.Position = UDim2.new(0, 20, 0, 200)
    timerBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    timerBarBG.BorderSizePixel = 2
    timerBarBG.BorderColor3 = Color3.new(1, 1, 1)
    timerBarBG.Parent = centralFrame
    
    -- Cantos arredondados para a barra
    local barCorner = Instance.new("UICorner")
    barCorner.CornerRadius = UDim.new(0, 8)
    barCorner.Parent = timerBarBG
    
    -- Barra de timer
    timerBar = Instance.new("Frame")
    timerBar.Size = UDim2.new(1, 0, 1, 0)
    timerBar.Position = UDim2.new(0, 0, 0, 0)
    timerBar.BackgroundColor3 = Color3.new(1, 0, 0)
    timerBar.BorderSizePixel = 0
    timerBar.Parent = timerBarBG
    
    -- Cantos arredondados para a barra interna
    local innerBarCorner = Instance.new("UICorner")
    innerBarCorner.CornerRadius = UDim.new(0, 6)
    innerBarCorner.Parent = timerBar
    
    -- Texto do timer
    timerText = Instance.new("TextLabel")
    timerText.Size = UDim2.new(1, 0, 0, 40)
    timerText.Position = UDim2.new(0, 0, 0, 240)
    timerText.BackgroundTransparency = 1
    timerText.Text = "10.0s"
    timerText.TextColor3 = Color3.new(1, 1, 0)
    timerText.TextScaled = true
    timerText.Font = Enum.Font.SourceSansBold
    timerText.Parent = centralFrame
    
    -- Instruções
    local instructionLabel = Instance.new("TextLabel")
    instructionLabel.Size = UDim2.new(1, 0, 0, 20)
    instructionLabel.Position = UDim2.new(0, 0, 1, -30)
    instructionLabel.BackgroundTransparency = 1
    instructionLabel.Text = "Você respawnará automaticamente na sua base"
    instructionLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    instructionLabel.TextScaled = true
    instructionLabel.Font = Enum.Font.SourceSans
    instructionLabel.Parent = centralFrame
end

-- Função para mostrar a tela de respawn
local function showRespawnScreen(deathCause, respawnTimeSeconds, hasPartner)
    if not screenGui then
        createRespawnUI()
    end
    
    isRespawning = true
    respawnTime = respawnTimeSeconds or 10
    maxRespawnTime = respawnTime
    
    -- Atualiza informações da morte
    local deathInfo = "Causa: " .. (deathCause or "Desconhecida") .. "\n"
    deathInfo = deathInfo .. "Base perdeu 20 de tamanho\n"
    deathInfo = deathInfo .. "Recursos carregados foram perdidos\n"
    
    if hasPartner then
        deathInfo = deathInfo .. "Tempo de respawn: 15s (Dupla)"
    else
        deathInfo = deathInfo .. "Tempo de respawn: 10s (Solo)"
    end
    
    if deathInfoText then
        deathInfoText.Text = deathInfo
    end
    
    -- Mostra a UI
    if respawnFrame then
        respawnFrame.Visible = true
    end
    
    -- Inicia o timer
    if respawnConnection then
        respawnConnection:Disconnect()
    end
    
    respawnConnection = RunService.Heartbeat:Connect(function()
        if respawnTime > 0 then
            respawnTime = respawnTime - (1/60) -- Reduz por frame
            
            -- Atualiza barra de progresso
            local progress = 1 - (respawnTime / maxRespawnTime)
            if timerBar then
                local targetSize = UDim2.new(progress, 0, 1, 0)
                timerBar.Size = targetSize
                
                -- Muda cor conforme o progresso
                if progress < 0.3 then
                    timerBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
                elseif progress < 0.7 then
                    timerBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
                else
                    timerBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
                end
            end
            
            -- Atualiza texto do timer
            if timerText then
                timerText.Text = string.format("%.1fs", math.max(0, respawnTime))
            end
        else
            -- Timer acabou
            hideRespawnScreen()
        end
    end)
    
    print("💀 Tela de respawn mostrada - " .. respawnTime .. "s")
end

-- Função para esconder a tela de respawn
local function hideRespawnScreen()
    isRespawning = false
    
    if respawnFrame then
        respawnFrame.Visible = false
    end
    
    if respawnConnection then
        respawnConnection:Disconnect()
        respawnConnection = nil
    end
    
    print("✅ Tela de respawn escondida")
end

-- Função para verificar se o jogador morreu
local function onCharacterAdded(character)
    local humanoid = character:WaitForChild("Humanoid")
    
    humanoid.Died:Connect(function()
        -- Determina tempo de respawn e se tem parceiro
        local hasPartner = false
        local respawnTimeSeconds = 10 -- Solo por padrão
        
        -- Verifica se tem base e parceiro
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")
                
                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    -- Tem base, verifica se tem parceiro
                    if (owner and owner.Value and owner.Value ~= player) or 
                       (partner and partner.Value and partner.Value ~= player) then
                        hasPartner = true
                        respawnTimeSeconds = 15
                    end
                    break
                end
            end
        end
        
        -- Mostra tela de respawn
        showRespawnScreen("Combate", respawnTimeSeconds, hasPartner)
    end)
end

-- Inicialização
local function initializeRespawnUI()
    createRespawnUI()
    
    -- Conecta eventos
    if player.Character then
        onCharacterAdded(player.Character)
    end
    
    player.CharacterAdded:Connect(onCharacterAdded)
end

-- Inicializa quando o jogador spawna
if player.Character then
    initializeRespawnUI()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeRespawnUI()
end)

print("RespawnUI carregado com sucesso!")
