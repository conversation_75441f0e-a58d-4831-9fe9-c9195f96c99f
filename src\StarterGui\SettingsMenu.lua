-- SettingsMenu.lua
-- Menu de configurações completo com controles de volume, qualidade gráfica e keybinds

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local SoundService = game:GetService("SoundService")
local Lighting = game:GetService("Lighting")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Variáveis da UI
local screenGui = nil
local settingsFrame = nil
local isSettingsOpen = false

-- Configurações padrão
local defaultSettings = {
    masterVolume = 0.5,
    sfxVolume = 0.7,
    musicVolume = 0.3,
    graphicsQuality = 2, -- 1=Baixa, 2=Média, 3=Alta
    showFPS = false,
    showPing = false,
    dashKey = Enum.KeyCode.LeftShift,
    buildKey = Enum.KeyCode.B,
    minimapKey = Enum.KeyCode.M,
    menuKey = Enum.KeyCode.Tab,
    chatEnabled = true,
    autoRespawn = true
}

-- Configurações atuais
local currentSettings = {}

-- Função para carregar configurações
local function loadSettings()
    -- Copia configurações padrão
    for key, value in pairs(defaultSettings) do
        currentSettings[key] = value
    end
    
    -- Aqui você poderia carregar de um DataStore se necessário
    print("Configurações carregadas")
end

-- Função para salvar configurações
local function saveSettings()
    -- Aqui você poderia salvar em um DataStore se necessário
    print("Configurações salvas")
end

-- Função para aplicar configurações
local function applySettings()
    -- Volume
    SoundService.Volume = currentSettings.masterVolume
    
    -- Qualidade gráfica
    local quality = currentSettings.graphicsQuality
    if quality == 1 then
        -- Baixa qualidade
        Lighting.GlobalShadows = false
        settings().Rendering.QualityLevel = Enum.QualityLevel.Level01
    elseif quality == 2 then
        -- Média qualidade
        Lighting.GlobalShadows = true
        settings().Rendering.QualityLevel = Enum.QualityLevel.Level10
    else
        -- Alta qualidade
        Lighting.GlobalShadows = true
        settings().Rendering.QualityLevel = Enum.QualityLevel.Level21
    end
    
    print("Configurações aplicadas")
end

-- Função para criar slider
local function createSlider(parent, name, minValue, maxValue, currentValue, callback)
    local sliderFrame = Instance.new("Frame")
    sliderFrame.Size = UDim2.new(1, -20, 0, 40)
    sliderFrame.BackgroundTransparency = 1
    sliderFrame.Parent = parent
    
    -- Label do slider
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(0.4, 0, 0, 20)
    label.Position = UDim2.new(0, 0, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextScaled = true
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Font = Enum.Font.SourceSans
    label.Parent = sliderFrame
    
    -- Background do slider
    local sliderBG = Instance.new("Frame")
    sliderBG.Size = UDim2.new(0.5, 0, 0, 10)
    sliderBG.Position = UDim2.new(0.4, 10, 0, 5)
    sliderBG.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
    sliderBG.BorderSizePixel = 1
    sliderBG.BorderColor3 = Color3.new(0.6, 0.6, 0.6)
    sliderBG.Parent = sliderFrame
    
    -- Barra do slider
    local sliderBar = Instance.new("Frame")
    sliderBar.Size = UDim2.new((currentValue - minValue) / (maxValue - minValue), 0, 1, 0)
    sliderBar.Position = UDim2.new(0, 0, 0, 0)
    sliderBar.BackgroundColor3 = Color3.new(0, 0.8, 1)
    sliderBar.BorderSizePixel = 0
    sliderBar.Parent = sliderBG
    
    -- Valor do slider
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Size = UDim2.new(0.1, 0, 0, 20)
    valueLabel.Position = UDim2.new(0.9, 10, 0, 0)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = tostring(math.floor(currentValue * 100))
    valueLabel.TextColor3 = Color3.new(1, 1, 1)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSans
    valueLabel.Parent = sliderFrame
    
    -- Botão invisível para capturar cliques
    local clickButton = Instance.new("TextButton")
    clickButton.Size = UDim2.new(0.5, 0, 1, 0)
    clickButton.Position = UDim2.new(0.4, 10, 0, 0)
    clickButton.BackgroundTransparency = 1
    clickButton.Text = ""
    clickButton.Parent = sliderFrame
    
    -- Função para atualizar slider
    local function updateSlider(mouseX)
        local relativeX = mouseX - sliderBG.AbsolutePosition.X
        local percentage = math.clamp(relativeX / sliderBG.AbsoluteSize.X, 0, 1)
        local value = minValue + (percentage * (maxValue - minValue))
        
        sliderBar.Size = UDim2.new(percentage, 0, 1, 0)
        valueLabel.Text = tostring(math.floor(value * 100))
        
        if callback then
            callback(value)
        end
    end
    
    -- Conecta eventos
    clickButton.MouseButton1Down:Connect(function()
        local connection
        connection = UserInputService.InputChanged:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseMovement then
                updateSlider(input.Position.X)
            end
        end)
        
        local releaseConnection
        releaseConnection = UserInputService.InputEnded:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                connection:Disconnect()
                releaseConnection:Disconnect()
            end
        end)
    end)
    
    clickButton.MouseButton1Click:Connect(function()
        updateSlider(UserInputService:GetMouseLocation().X)
    end)
    
    return sliderFrame
end

-- Função para criar dropdown
local function createDropdown(parent, name, options, currentIndex, callback)
    local dropdownFrame = Instance.new("Frame")
    dropdownFrame.Size = UDim2.new(1, -20, 0, 40)
    dropdownFrame.BackgroundTransparency = 1
    dropdownFrame.Parent = parent
    
    -- Label do dropdown
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(0.4, 0, 0, 20)
    label.Position = UDim2.new(0, 0, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextScaled = true
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Font = Enum.Font.SourceSans
    label.Parent = dropdownFrame
    
    -- Botão do dropdown
    local dropdownButton = Instance.new("TextButton")
    dropdownButton.Size = UDim2.new(0.5, 0, 0, 25)
    dropdownButton.Position = UDim2.new(0.4, 10, 0, 0)
    dropdownButton.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    dropdownButton.BorderSizePixel = 1
    dropdownButton.BorderColor3 = Color3.new(0.6, 0.6, 0.6)
    dropdownButton.Text = options[currentIndex] .. " ▼"
    dropdownButton.TextColor3 = Color3.new(1, 1, 1)
    dropdownButton.TextScaled = true
    dropdownButton.Font = Enum.Font.SourceSans
    dropdownButton.Parent = dropdownFrame
    
    -- Lista de opções (inicialmente invisível)
    local optionsList = Instance.new("Frame")
    optionsList.Size = UDim2.new(0.5, 0, 0, #options * 25)
    optionsList.Position = UDim2.new(0.4, 10, 0, 25)
    optionsList.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    optionsList.BorderSizePixel = 1
    optionsList.BorderColor3 = Color3.new(0.6, 0.6, 0.6)
    optionsList.Visible = false
    optionsList.ZIndex = 10
    optionsList.Parent = dropdownFrame
    
    -- Cria botões para cada opção
    for i, option in ipairs(options) do
        local optionButton = Instance.new("TextButton")
        optionButton.Size = UDim2.new(1, 0, 0, 25)
        optionButton.Position = UDim2.new(0, 0, 0, (i-1) * 25)
        optionButton.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
        optionButton.BorderSizePixel = 1
        optionButton.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
        optionButton.Text = option
        optionButton.TextColor3 = Color3.new(1, 1, 1)
        optionButton.TextScaled = true
        optionButton.Font = Enum.Font.SourceSans
        optionButton.ZIndex = 11
        optionButton.Parent = optionsList
        
        optionButton.MouseButton1Click:Connect(function()
            dropdownButton.Text = option .. " ▼"
            optionsList.Visible = false
            if callback then
                callback(i)
            end
        end)
    end
    
    -- Conecta evento do botão principal
    dropdownButton.MouseButton1Click:Connect(function()
        optionsList.Visible = not optionsList.Visible
    end)
    
    return dropdownFrame
end

-- Função para criar o menu de configurações
local function createSettingsMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("SettingsMenu")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "SettingsMenu"
    screenGui.Parent = playerGui
    
    -- Frame principal (inicialmente invisível)
    settingsFrame = Instance.new("Frame")
    settingsFrame.Name = "SettingsFrame"
    settingsFrame.Size = UDim2.new(0, 600, 0, 500)
    settingsFrame.Position = UDim2.new(0.5, -300, 0.5, -250)
    settingsFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    settingsFrame.BackgroundTransparency = 0.1
    settingsFrame.BorderSizePixel = 3
    settingsFrame.BorderColor3 = Color3.new(0, 1, 1)
    settingsFrame.Visible = false
    settingsFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = settingsFrame
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 50)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.Text = "⚙️ CONFIGURAÇÕES"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = settingsFrame
    
    -- Cantos arredondados para o título
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleLabel
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(1, 0, 0)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = settingsFrame
    
    -- ScrollingFrame para conteúdo
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 1, -100)
    scrollFrame.Position = UDim2.new(0, 10, 0, 60)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 10
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 600)
    scrollFrame.Parent = settingsFrame
    
    local yPos = 10
    
    -- Seção de Áudio
    local audioTitle = Instance.new("TextLabel")
    audioTitle.Size = UDim2.new(1, 0, 0, 30)
    audioTitle.Position = UDim2.new(0, 0, 0, yPos)
    audioTitle.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    audioTitle.Text = "🔊 ÁUDIO"
    audioTitle.TextColor3 = Color3.new(1, 1, 1)
    audioTitle.TextScaled = true
    audioTitle.Font = Enum.Font.SourceSansBold
    audioTitle.Parent = scrollFrame
    yPos = yPos + 40
    
    -- Sliders de volume
    createSlider(scrollFrame, "Volume Geral:", 0, 1, currentSettings.masterVolume, function(value)
        currentSettings.masterVolume = value
    end).Position = UDim2.new(0, 0, 0, yPos)
    yPos = yPos + 50
    
    createSlider(scrollFrame, "Efeitos Sonoros:", 0, 1, currentSettings.sfxVolume, function(value)
        currentSettings.sfxVolume = value
    end).Position = UDim2.new(0, 0, 0, yPos)
    yPos = yPos + 50
    
    createSlider(scrollFrame, "Música:", 0, 1, currentSettings.musicVolume, function(value)
        currentSettings.musicVolume = value
    end).Position = UDim2.new(0, 0, 0, yPos)
    yPos = yPos + 60
    
    -- Seção de Gráficos
    local graphicsTitle = Instance.new("TextLabel")
    graphicsTitle.Size = UDim2.new(1, 0, 0, 30)
    graphicsTitle.Position = UDim2.new(0, 0, 0, yPos)
    graphicsTitle.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    graphicsTitle.Text = "🎨 GRÁFICOS"
    graphicsTitle.TextColor3 = Color3.new(1, 1, 1)
    graphicsTitle.TextScaled = true
    graphicsTitle.Font = Enum.Font.SourceSansBold
    graphicsTitle.Parent = scrollFrame
    yPos = yPos + 40
    
    -- Dropdown de qualidade
    createDropdown(scrollFrame, "Qualidade Gráfica:", {"Baixa", "Média", "Alta"}, currentSettings.graphicsQuality, function(index)
        currentSettings.graphicsQuality = index
    end).Position = UDim2.new(0, 0, 0, yPos)
    yPos = yPos + 60
    
    -- Botões de aplicar e resetar
    local buttonFrame = Instance.new("Frame")
    buttonFrame.Size = UDim2.new(1, -20, 0, 40)
    buttonFrame.Position = UDim2.new(0, 10, 1, -50)
    buttonFrame.BackgroundTransparency = 1
    buttonFrame.Parent = settingsFrame
    
    local applyButton = Instance.new("TextButton")
    applyButton.Size = UDim2.new(0.3, 0, 1, 0)
    applyButton.Position = UDim2.new(0, 0, 0, 0)
    applyButton.BackgroundColor3 = Color3.new(0, 0.7, 0)
    applyButton.Text = "APLICAR"
    applyButton.TextColor3 = Color3.new(1, 1, 1)
    applyButton.TextScaled = true
    applyButton.Font = Enum.Font.SourceSansBold
    applyButton.Parent = buttonFrame
    
    local resetButton = Instance.new("TextButton")
    resetButton.Size = UDim2.new(0.3, 0, 1, 0)
    resetButton.Position = UDim2.new(0.35, 0, 0, 0)
    resetButton.BackgroundColor3 = Color3.new(0.7, 0.7, 0)
    resetButton.Text = "RESETAR"
    resetButton.TextColor3 = Color3.new(1, 1, 1)
    resetButton.TextScaled = true
    resetButton.Font = Enum.Font.SourceSansBold
    resetButton.Parent = buttonFrame
    
    local cancelButton = Instance.new("TextButton")
    cancelButton.Size = UDim2.new(0.3, 0, 1, 0)
    cancelButton.Position = UDim2.new(0.7, 0, 0, 0)
    cancelButton.BackgroundColor3 = Color3.new(0.7, 0, 0)
    cancelButton.Text = "CANCELAR"
    cancelButton.TextColor3 = Color3.new(1, 1, 1)
    cancelButton.TextScaled = true
    cancelButton.Font = Enum.Font.SourceSansBold
    cancelButton.Parent = buttonFrame
    
    -- Conecta eventos dos botões
    closeButton.MouseButton1Click:Connect(function()
        toggleSettings()
    end)
    
    applyButton.MouseButton1Click:Connect(function()
        applySettings()
        saveSettings()
        toggleSettings()
    end)
    
    resetButton.MouseButton1Click:Connect(function()
        loadSettings()
        -- Aqui você recriaria o menu com os valores padrão
    end)
    
    cancelButton.MouseButton1Click:Connect(function()
        loadSettings()
        toggleSettings()
    end)
end

-- Função para alternar visibilidade do menu
function toggleSettings()
    if not settingsFrame then return end
    
    isSettingsOpen = not isSettingsOpen
    settingsFrame.Visible = isSettingsOpen
    
    if isSettingsOpen then
        print("⚙️ Menu de configurações aberto")
    else
        print("⚙️ Menu de configurações fechado")
    end
end

-- Inicialização
local function initializeSettings()
    loadSettings()
    createSettingsMenu()
    applySettings()
    
    -- Conecta tecla ESC para abrir configurações
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.Escape then
            toggleSettings()
        end
    end)
end

-- Inicializa quando o jogador spawna
if player.Character then
    initializeSettings()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeSettings()
end)

print("SettingsMenu carregado com sucesso!")
