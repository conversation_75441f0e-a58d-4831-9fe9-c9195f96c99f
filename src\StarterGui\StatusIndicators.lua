-- StatusIndicators.lua
-- Indicadores avançados de status para todos os estados do jogo

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Variáveis da UI
local screenGui = nil
local statusContainer = nil
local activeIndicators = {}

-- Estados do jogador
local playerStates = {
    carryingResources = false,
    resourceCount = 0,
    inBarrier = false,
    inOwnBarrier = false,
    barrierOwner = nil,
    isSlowed = false,
    isBuilding = false,
    dashAvailable = true,
    dashCooldown = 0,
    healthRegenActive = false,
    takingDamage = false,
    respawning = false,
    invulnerable = false
}

-- Configurações dos indicadores
local INDICATOR_CONFIGS = {
    CARRYING_RESOURCES = {
        icon = "💎",
        color = Color3.new(1, 1, 0),
        text = "Carregando Recursos",
        priority = 1,
        blinking = true
    },
    SLOWED = {
        icon = "🐌",
        color = Color3.new(1, 0.5, 0),
        text = "Movimento Lento",
        priority = 2,
        blinking = false
    },
    IN_OWN_BARRIER = {
        icon = "🛡️",
        color = Color3.new(0, 1, 0),
        text = "Na Barreira Própria - Curando",
        priority = 3,
        blinking = false
    },
    IN_ENEMY_BARRIER = {
        icon = "⚠️",
        color = Color3.new(1, 0, 0),
        text = "Na Barreira Inimiga - Tomando Dano!",
        priority = 4,
        blinking = true
    },
    BUILDING_MODE = {
        icon = "🔨",
        color = Color3.new(0, 0.8, 1),
        text = "Modo Construção",
        priority = 5,
        blinking = false
    },
    DASH_COOLDOWN = {
        icon = "⚡",
        color = Color3.new(0.7, 0.7, 0.7),
        text = "Dash em Cooldown",
        priority = 6,
        blinking = false
    },
    HEALTH_REGEN = {
        icon = "❤️",
        color = Color3.new(0, 1, 0.5),
        text = "Regenerando Vida",
        priority = 7,
        blinking = false
    },
    TAKING_DAMAGE = {
        icon = "💥",
        color = Color3.new(1, 0.2, 0.2),
        text = "Tomando Dano",
        priority = 8,
        blinking = true
    },
    INVULNERABLE = {
        icon = "✨",
        color = Color3.new(1, 1, 1),
        text = "Invulnerável",
        priority = 9,
        blinking = true
    }
}

-- Função para criar o container de indicadores
local function createStatusContainer()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("StatusIndicators")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "StatusIndicators"
    screenGui.Parent = playerGui
    
    -- Container principal dos indicadores (centro-superior da tela)
    statusContainer = Instance.new("Frame")
    statusContainer.Name = "StatusContainer"
    statusContainer.Size = UDim2.new(0, 400, 0, 200)
    statusContainer.Position = UDim2.new(0.5, -200, 0, 60)
    statusContainer.BackgroundTransparency = 1
    statusContainer.Parent = screenGui
end

-- Função para criar um indicador individual
local function createIndicator(statusType, config)
    local indicatorFrame = Instance.new("Frame")
    indicatorFrame.Name = statusType .. "_Indicator"
    indicatorFrame.Size = UDim2.new(0, 300, 0, 40)
    indicatorFrame.BackgroundColor3 = config.color
    indicatorFrame.BackgroundTransparency = 0.2
    indicatorFrame.BorderSizePixel = 2
    indicatorFrame.BorderColor3 = Color3.new(1, 1, 1)
    indicatorFrame.Parent = statusContainer
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = indicatorFrame
    
    -- Ícone
    local iconLabel = Instance.new("TextLabel")
    iconLabel.Size = UDim2.new(0, 35, 1, 0)
    iconLabel.Position = UDim2.new(0, 0, 0, 0)
    iconLabel.BackgroundTransparency = 1
    iconLabel.Text = config.icon
    iconLabel.TextColor3 = Color3.new(1, 1, 1)
    iconLabel.TextScaled = true
    iconLabel.Font = Enum.Font.SourceSansBold
    iconLabel.Parent = indicatorFrame
    
    -- Texto
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, -40, 1, 0)
    textLabel.Position = UDim2.new(0, 40, 0, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = config.text
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.TextXAlignment = Enum.TextXAlignment.Left
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = indicatorFrame
    
    -- Efeito de piscar se necessário
    if config.blinking then
        local blinkTween = TweenService:Create(
            indicatorFrame,
            TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            {BackgroundTransparency = 0.7}
        )
        blinkTween:Play()
    end
    
    -- Animação de entrada
    indicatorFrame.Position = UDim2.new(0.5, -150, 0, -50)
    local enterTween = TweenService:Create(
        indicatorFrame,
        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = UDim2.new(0.5, -150, 0, 0)}
    )
    enterTween:Play()
    
    return indicatorFrame
end

-- Função para mostrar indicador
local function showIndicator(statusType)
    if activeIndicators[statusType] then return end -- Já está ativo
    
    local config = INDICATOR_CONFIGS[statusType]
    if not config then return end
    
    local indicator = createIndicator(statusType, config)
    activeIndicators[statusType] = {
        frame = indicator,
        config = config,
        priority = config.priority
    }
    
    repositionIndicators()
end

-- Função para esconder indicador
local function hideIndicator(statusType)
    if not activeIndicators[statusType] then return end
    
    local indicator = activeIndicators[statusType]
    
    -- Animação de saída
    local exitTween = TweenService:Create(
        indicator.frame,
        TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
        {
            Position = UDim2.new(0.5, -150, 0, -50),
            BackgroundTransparency = 1
        }
    )
    
    exitTween:Play()
    exitTween.Completed:Connect(function()
        indicator.frame:Destroy()
    end)
    
    activeIndicators[statusType] = nil
    repositionIndicators()
end

-- Função para reposicionar indicadores por prioridade
local function repositionIndicators()
    -- Ordena indicadores por prioridade
    local sortedIndicators = {}
    for statusType, indicator in pairs(activeIndicators) do
        table.insert(sortedIndicators, {
            type = statusType,
            indicator = indicator
        })
    end
    
    table.sort(sortedIndicators, function(a, b)
        return a.indicator.priority < b.indicator.priority
    end)
    
    -- Reposiciona
    for i, item in ipairs(sortedIndicators) do
        local targetPosition = UDim2.new(0.5, -150, 0, (i-1) * 45)
        local repositionTween = TweenService:Create(
            item.indicator.frame,
            TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Position = targetPosition}
        )
        repositionTween:Play()
    end
end

-- Função para atualizar estados do jogador
local function updatePlayerStates()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    local humanoidRootPart = player.Character.HumanoidRootPart
    local playerPosition = humanoidRootPart.Position
    
    -- Verifica recursos carregados
    local resourceCount = 0
    for _, child in ipairs(humanoidRootPart:GetChildren()) do
        if child.Name == "CarryingResource" then
            resourceCount = resourceCount + 1
        end
    end
    
    local wasCarrying = playerStates.carryingResources
    playerStates.carryingResources = resourceCount > 0
    playerStates.resourceCount = resourceCount
    
    -- Verifica lentidão (quando carregando recursos)
    playerStates.isSlowed = playerStates.carryingResources and humanoid.WalkSpeed < 32
    
    -- Verifica barreira
    local inBarrier = false
    local inOwnBarrier = false
    local barrierOwner = nil
    
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local barrier = base:FindFirstChild("Barrier")
            if barrier then
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2
                
                if distance <= barrierRadius then
                    inBarrier = true
                    local owner = base:FindFirstChild("Owner")
                    local partner = base:FindFirstChild("Partner")
                    
                    if (owner and owner.Value == player) or (partner and partner.Value == player) then
                        inOwnBarrier = true
                        barrierOwner = "própria"
                    else
                        inOwnBarrier = false
                        if owner and owner.Value then
                            barrierOwner = owner.Value.Name
                        else
                            barrierOwner = "inimiga"
                        end
                    end
                    break
                end
            end
        end
    end
    
    playerStates.inBarrier = inBarrier
    playerStates.inOwnBarrier = inOwnBarrier
    playerStates.barrierOwner = barrierOwner
    
    -- Verifica regeneração de vida
    local currentHealth = humanoid.Health
    local maxHealth = humanoid.MaxHealth
    playerStates.healthRegenActive = inOwnBarrier and currentHealth < maxHealth
    
    -- Verifica dash (se existe sistema global)
    if _G.DashSystem then
        playerStates.dashAvailable = not _G.DashSystem.isOnCooldown()
        playerStates.dashCooldown = _G.DashSystem.getCooldownTime()
    end
    
    -- Verifica modo construção (se existe sistema global)
    if _G.BuildingSystem then
        playerStates.isBuilding = _G.BuildingSystem.isBuildingMode()
    end
end

-- Função para atualizar indicadores baseado nos estados
local function updateIndicators()
    -- Carregando recursos
    if playerStates.carryingResources then
        showIndicator("CARRYING_RESOURCES")
        -- Atualiza texto com quantidade
        if activeIndicators["CARRYING_RESOURCES"] then
            local textLabel = activeIndicators["CARRYING_RESOURCES"].frame:FindFirstChild("TextLabel")
            if textLabel then
                textLabel.Text = "Carregando " .. playerStates.resourceCount .. " Recursos"
            end
        end
    else
        hideIndicator("CARRYING_RESOURCES")
    end
    
    -- Lentidão
    if playerStates.isSlowed then
        showIndicator("SLOWED")
    else
        hideIndicator("SLOWED")
    end
    
    -- Barreira própria
    if playerStates.inOwnBarrier then
        showIndicator("IN_OWN_BARRIER")
    else
        hideIndicator("IN_OWN_BARRIER")
    end
    
    -- Barreira inimiga
    if playerStates.inBarrier and not playerStates.inOwnBarrier then
        showIndicator("IN_ENEMY_BARRIER")
        -- Atualiza texto com dono da barreira
        if activeIndicators["IN_ENEMY_BARRIER"] then
            local textLabel = activeIndicators["IN_ENEMY_BARRIER"].frame:FindFirstChild("TextLabel")
            if textLabel then
                textLabel.Text = "Na Barreira de " .. (playerStates.barrierOwner or "Inimigo") .. " - Tomando Dano!"
            end
        end
    else
        hideIndicator("IN_ENEMY_BARRIER")
    end
    
    -- Modo construção
    if playerStates.isBuilding then
        showIndicator("BUILDING_MODE")
    else
        hideIndicator("BUILDING_MODE")
    end
    
    -- Dash em cooldown
    if not playerStates.dashAvailable then
        showIndicator("DASH_COOLDOWN")
        -- Atualiza texto com tempo restante
        if activeIndicators["DASH_COOLDOWN"] then
            local textLabel = activeIndicators["DASH_COOLDOWN"].frame:FindFirstChild("TextLabel")
            if textLabel then
                textLabel.Text = "Dash em Cooldown (" .. string.format("%.1f", playerStates.dashCooldown) .. "s)"
            end
        end
    else
        hideIndicator("DASH_COOLDOWN")
    end
    
    -- Regeneração de vida
    if playerStates.healthRegenActive then
        showIndicator("HEALTH_REGEN")
    else
        hideIndicator("HEALTH_REGEN")
    end
    
    -- Tomando dano (detecta pela mudança rápida de vida)
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        local currentHealth = humanoid.Health
        
        -- Simples detecção de dano (pode ser melhorada)
        if playerStates.lastHealth and currentHealth < playerStates.lastHealth - 5 then
            playerStates.takingDamage = true
            showIndicator("TAKING_DAMAGE")
            
            -- Remove indicador após 2 segundos
            task.wait(2)
            playerStates.takingDamage = false
            hideIndicator("TAKING_DAMAGE")
        end
        
        playerStates.lastHealth = currentHealth
    end
end

-- Função para adicionar indicador customizado
local function addCustomIndicator(statusType, icon, color, text, duration)
    INDICATOR_CONFIGS[statusType] = {
        icon = icon,
        color = color,
        text = text,
        priority = 10,
        blinking = false
    }
    
    showIndicator(statusType)
    
    if duration then
        task.wait(duration)
        hideIndicator(statusType)
        INDICATOR_CONFIGS[statusType] = nil
    end
end

-- Inicialização
local function initializeStatusIndicators()
    createStatusContainer()
    
    -- Loop de atualização
    RunService.Heartbeat:Connect(function()
        updatePlayerStates()
        updateIndicators()
    end)
    
    -- Conecta tecla I para mostrar/esconder indicadores
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.I then
            statusContainer.Visible = not statusContainer.Visible
        end
    end)
end

-- Exporta funções para uso global
_G.StatusIndicators = {
    show = showIndicator,
    hide = hideIndicator,
    addCustom = addCustomIndicator,
    updateState = function(stateName, value)
        if playerStates[stateName] ~= nil then
            playerStates[stateName] = value
        end
    end
}

-- Inicializa quando o jogador spawna
if player.Character then
    initializeStatusIndicators()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    initializeStatusIndicators()
end)

print("StatusIndicators carregado com sucesso!")
