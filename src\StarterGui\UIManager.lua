-- UIManager.lua
-- Gerenciador principal da UI completa e funcional

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Variáveis de controle
local uiComponents = {}
local isUIInitialized = false
local performanceMode = false

-- Configurações da UI
local UI_CONFIG = {
    ENABLE_HUD = true,
    ENABLE_MINIMAP = true,
    ENABLE_NOTIFICATIONS = true,
    ENABLE_ACHIEVEMENTS = true,
    ENABLE_SETTINGS = true,
    ENABLE_STATUS_INDICATORS = true,
    ENABLE_RESPAWN_UI = true,
    ENABLE_BUILDING_UI = true,
    ENABLE_INVITE_UI = true,
    
    -- Performance
    UPDATE_RATE = 60, -- FPS para atualizações da UI
    REDUCE_EFFECTS = false,
    SHOW_DEBUG_INFO = false
}

-- Função para verificar dependências
local function checkDependencies()
    local dependencies = {
        "RemoteEvents"
    }
    
    for _, dep in ipairs(dependencies) do
        if not ReplicatedStorage:FindFirstChild(dep) then
            warn("UIManager: Dependência não encontrada: " .. dep)
            return false
        end
    end
    
    return true
end

-- Função para inicializar componente individual
local function initializeComponent(componentName, enabled)
    if not enabled then
        print("UIManager: " .. componentName .. " desabilitado")
        return false
    end
    
    local success, error = pcall(function()
        -- Os componentes já se inicializam automaticamente
        -- Esta função serve para controle e debug
        print("UIManager: " .. componentName .. " inicializado")
    end)
    
    if not success then
        warn("UIManager: Erro ao inicializar " .. componentName .. ": " .. tostring(error))
        return false
    end
    
    return true
end

-- Função para criar indicador de FPS/Performance
local function createPerformanceIndicator()
    if not UI_CONFIG.SHOW_DEBUG_INFO then return end
    
    local playerGui = player:WaitForChild("PlayerGui")
    
    local perfGui = Instance.new("ScreenGui")
    perfGui.Name = "PerformanceIndicator"
    perfGui.Parent = playerGui
    
    local perfFrame = Instance.new("Frame")
    perfFrame.Size = UDim2.new(0, 150, 0, 80)
    perfFrame.Position = UDim2.new(0, 10, 1, -90)
    perfFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    perfFrame.BackgroundTransparency = 0.5
    perfFrame.BorderSizePixel = 1
    perfFrame.BorderColor3 = Color3.new(1, 1, 1)
    perfFrame.Parent = perfGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 5)
    corner.Parent = perfFrame
    
    local fpsLabel = Instance.new("TextLabel")
    fpsLabel.Size = UDim2.new(1, 0, 0.5, 0)
    fpsLabel.Position = UDim2.new(0, 0, 0, 0)
    fpsLabel.BackgroundTransparency = 1
    fpsLabel.Text = "FPS: 60"
    fpsLabel.TextColor3 = Color3.new(0, 1, 0)
    fpsLabel.TextScaled = true
    fpsLabel.Font = Enum.Font.SourceSansBold
    fpsLabel.Parent = perfFrame
    
    local pingLabel = Instance.new("TextLabel")
    pingLabel.Size = UDim2.new(1, 0, 0.5, 0)
    pingLabel.Position = UDim2.new(0, 0, 0.5, 0)
    pingLabel.BackgroundTransparency = 1
    pingLabel.Text = "Ping: 0ms"
    pingLabel.TextColor3 = Color3.new(0, 1, 1)
    pingLabel.TextScaled = true
    pingLabel.Font = Enum.Font.SourceSansBold
    pingLabel.Parent = perfFrame
    
    -- Atualiza FPS
    local frameCount = 0
    local lastTime = tick()
    
    RunService.Heartbeat:Connect(function()
        frameCount = frameCount + 1
        local currentTime = tick()
        
        if currentTime - lastTime >= 1 then
            local fps = frameCount / (currentTime - lastTime)
            fpsLabel.Text = "FPS: " .. math.floor(fps)
            
            -- Muda cor baseado no FPS
            if fps >= 50 then
                fpsLabel.TextColor3 = Color3.new(0, 1, 0) -- Verde
            elseif fps >= 30 then
                fpsLabel.TextColor3 = Color3.new(1, 1, 0) -- Amarelo
            else
                fpsLabel.TextColor3 = Color3.new(1, 0, 0) -- Vermelho
            end
            
            frameCount = 0
            lastTime = currentTime
        end
        
        -- Atualiza ping (simulado)
        local ping = math.random(10, 100)
        pingLabel.Text = "Ping: " .. ping .. "ms"
    end)
end

-- Função para criar menu de ajuda
local function createHelpMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    local helpGui = Instance.new("ScreenGui")
    helpGui.Name = "HelpMenu"
    helpGui.Parent = playerGui
    
    local helpFrame = Instance.new("Frame")
    helpFrame.Size = UDim2.new(0, 400, 0, 300)
    helpFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
    helpFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    helpFrame.BackgroundTransparency = 0.1
    helpFrame.BorderSizePixel = 2
    helpFrame.BorderColor3 = Color3.new(0, 1, 1)
    helpFrame.Visible = false
    helpFrame.Parent = helpGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = helpFrame
    
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0, 0.8, 0.8)
    titleLabel.Text = "❓ AJUDA - CONTROLES"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = helpFrame
    
    local helpText = Instance.new("TextLabel")
    helpText.Size = UDim2.new(1, -20, 1, -80)
    helpText.Position = UDim2.new(0, 10, 0, 50)
    helpText.BackgroundTransparency = 1
    helpText.Text = [[
🎮 CONTROLES PRINCIPAIS:
• WASD - Movimento
• Mouse - Olhar ao redor
• Clique Esquerdo - Atirar/Usar ferramenta
• Shift Esquerdo - Dash (5s cooldown)

🔧 INTERFACE:
• TAB - Menu do jogador
• B - Menu de construção (na base)
• M - Alternar mini-mapa
• P - Conquistas e estatísticas
• ESC - Configurações
• I - Alternar indicadores de status

🏠 GAMEPLAY:
• Toque no ClaimPad amarelo para reivindicar base
• Colete recursos e deposite na sua base
• Construa defesas dentro da barreira
• Forme duplas para respawn mais lento
• Ataque bases inimigas para destruí-las
    ]]
    helpText.TextColor3 = Color3.new(1, 1, 1)
    helpText.TextScaled = true
    helpText.TextWrapped = true
    helpText.TextXAlignment = Enum.TextXAlignment.Left
    helpText.TextYAlignment = Enum.TextYAlignment.Top
    helpText.Font = Enum.Font.SourceSans
    helpText.Parent = helpFrame
    
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 100, 0, 30)
    closeButton.Position = UDim2.new(0.5, -50, 1, -40)
    closeButton.BackgroundColor3 = Color3.new(0.7, 0, 0)
    closeButton.Text = "FECHAR"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = helpFrame
    
    closeButton.MouseButton1Click:Connect(function()
        helpFrame.Visible = false
    end)
    
    -- Conecta tecla F1 para abrir ajuda
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F1 then
            helpFrame.Visible = not helpFrame.Visible
        end
    end)
end

-- Função para otimizar performance
local function optimizePerformance()
    if not performanceMode then return end
    
    -- Reduz taxa de atualização de alguns componentes
    UI_CONFIG.UPDATE_RATE = 30
    UI_CONFIG.REDUCE_EFFECTS = true
    
    print("UIManager: Modo de performance ativado")
end

-- Função para verificar integridade da UI
local function checkUIIntegrity()
    local playerGui = player:WaitForChild("PlayerGui")
    local expectedComponents = {
        "MainHUD",
        "NotificationSystem", 
        "MinimapRadar",
        "SettingsMenu",
        "AchievementSystem",
        "StatusIndicators",
        "RespawnUI",
        "BuildingUI",
        "InviteUI"
    }
    
    local missingComponents = {}
    
    for _, componentName in ipairs(expectedComponents) do
        if not playerGui:FindFirstChild(componentName) then
            table.insert(missingComponents, componentName)
        end
    end
    
    if #missingComponents > 0 then
        warn("UIManager: Componentes ausentes: " .. table.concat(missingComponents, ", "))
        return false
    end
    
    print("UIManager: Todos os componentes da UI estão presentes")
    return true
end

-- Função para mostrar notificação de boas-vindas
local function showWelcomeMessage()
    task.wait(3) -- Aguarda outros sistemas carregarem
    
    if _G.NotificationSystem then
        _G.NotificationSystem.show("🎮 UI Completa Carregada! Pressione F1 para ajuda", "SUCCESS", 5)
    end
end

-- Função principal de inicialização
local function initializeUI()
    if isUIInitialized then
        warn("UIManager: UI já foi inicializada")
        return
    end
    
    print("UIManager: Inicializando UI completa...")
    
    -- Verifica dependências
    if not checkDependencies() then
        warn("UIManager: Falha na verificação de dependências")
        return
    end
    
    -- Otimiza performance se necessário
    optimizePerformance()
    
    -- Inicializa componentes individuais
    local components = {
        {"MainHUD", UI_CONFIG.ENABLE_HUD},
        {"NotificationSystem", UI_CONFIG.ENABLE_NOTIFICATIONS},
        {"MinimapRadar", UI_CONFIG.ENABLE_MINIMAP},
        {"SettingsMenu", UI_CONFIG.ENABLE_SETTINGS},
        {"AchievementSystem", UI_CONFIG.ENABLE_ACHIEVEMENTS},
        {"StatusIndicators", UI_CONFIG.ENABLE_STATUS_INDICATORS},
        {"RespawnUI", UI_CONFIG.ENABLE_RESPAWN_UI},
        {"BuildingUI", UI_CONFIG.ENABLE_BUILDING_UI},
        {"InviteUI", UI_CONFIG.ENABLE_INVITE_UI}
    }
    
    for _, component in ipairs(components) do
        initializeComponent(component[1], component[2])
    end
    
    -- Cria componentes adicionais
    createPerformanceIndicator()
    createHelpMenu()
    
    -- Verifica integridade após inicialização
    task.wait(2)
    checkUIIntegrity()
    
    -- Mostra mensagem de boas-vindas
    showWelcomeMessage()
    
    isUIInitialized = true
    print("UIManager: UI completa inicializada com sucesso!")
end

-- Função para recarregar UI
local function reloadUI()
    print("UIManager: Recarregando UI...")
    
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove todas as UIs existentes
    local uiComponents = {
        "MainHUD", "NotificationSystem", "MinimapRadar", "SettingsMenu",
        "AchievementSystem", "StatusIndicators", "RespawnUI", "BuildingUI",
        "InviteUI", "PerformanceIndicator", "HelpMenu"
    }
    
    for _, componentName in ipairs(uiComponents) do
        local component = playerGui:FindFirstChild(componentName)
        if component then
            component:Destroy()
        end
    end
    
    isUIInitialized = false
    task.wait(1)
    initializeUI()
end

-- Conecta eventos globais
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    -- F5 para recarregar UI
    if input.KeyCode == Enum.KeyCode.F5 then
        reloadUI()
    end
    
    -- F12 para alternar modo debug
    if input.KeyCode == Enum.KeyCode.F12 then
        UI_CONFIG.SHOW_DEBUG_INFO = not UI_CONFIG.SHOW_DEBUG_INFO
        if UI_CONFIG.SHOW_DEBUG_INFO then
            createPerformanceIndicator()
        else
            local perfIndicator = player.PlayerGui:FindFirstChild("PerformanceIndicator")
            if perfIndicator then
                perfIndicator:Destroy()
            end
        end
    end
end)

-- Exporta funções para uso global
_G.UIManager = {
    reload = reloadUI,
    isInitialized = function() return isUIInitialized end,
    setPerformanceMode = function(enabled) 
        performanceMode = enabled
        optimizePerformance()
    end,
    getConfig = function() return UI_CONFIG end,
    setConfig = function(newConfig)
        for key, value in pairs(newConfig) do
            if UI_CONFIG[key] ~= nil then
                UI_CONFIG[key] = value
            end
        end
    end
}

-- Inicializa quando o jogador spawna
if player.Character then
    initializeUI()
end

player.CharacterAdded:Connect(function()
    task.wait(2) -- Aguarda outros sistemas carregarem
    initializeUI()
end)

print("UIManager carregado com sucesso!")
