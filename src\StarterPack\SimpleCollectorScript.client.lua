-- SimpleCollectorScript.client.lua
-- Script simples para CollectorGun que funciona diretamente

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Configurações da ferramenta
local RANGE = 150
local COLLECT_RATE = 0.1

-- Controle de uso
local isCollecting = false
local currentTarget = nil
local collectConnection = nil

-- Função para criar efeito visual do raio
local function createBeamEffect(startPos, endPos, color)
    local beam = Instance.new("Part")
    beam.Name = "CollectorBeam"
    beam.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    beam.BrickColor = color or BrickColor.new("Bright blue")
    beam.Material = Enum.Material.Neon
    beam.CanCollide = false
    beam.Anchored = true
    beam.Parent = workspace
    
    -- Posiciona o raio
    beam.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -beam.Size.Z / 2)
    
    -- Efeito de fade
    local transparency = 0
    local connection
    connection = RunService.Heartbeat:Connect(function()
        transparency = transparency + 0.1
        beam.Transparency = transparency
        
        if transparency >= 1 then
            connection:Disconnect()
            beam:Destroy()
        end
    end)
    
    return beam
end

-- Função para verificar se um objeto é coletável
local function isCollectable(obj)
    return obj:FindFirstChild("OriginalSize") and obj.Parent == workspace
end

-- Função para coletar recurso (versão simplificada)
local function collectResource(resource)
    if not resource or not resource.Parent then return end
    
    -- Encolhe o recurso
    local currentSize = resource.Size
    local newSize = currentSize * 0.95
    
    if newSize.X < 0.5 or newSize.Y < 0.5 or newSize.Z < 0.5 then
        -- Recurso coletado completamente
        resource:Destroy()
        
        -- Cria recurso carregado no jogador
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local carryingResource = Instance.new("Part")
            carryingResource.Name = "CarryingResource"
            carryingResource.Size = Vector3.new(1, 1, 1)
            carryingResource.BrickColor = BrickColor.new("Bright yellow")
            carryingResource.Material = Enum.Material.Neon
            carryingResource.CanCollide = false
            carryingResource.Anchored = false
            carryingResource.Parent = player.Character.HumanoidRootPart
            
            local weld = Instance.new("WeldConstraint")
            weld.Part0 = player.Character.HumanoidRootPart
            weld.Part1 = carryingResource
            weld.Parent = carryingResource
            
            print("💎 " .. player.Name .. " coletou um recurso!")
        end
    else
        -- Continua encolhendo
        resource.Size = newSize
    end
end

-- Função para verificar se um objeto é uma base inimiga
local function isEnemyBase(obj)
    local baseModel = obj.Parent
    if baseModel and baseModel:FindFirstChild("Owner") and baseModel:FindFirstChild("BaseSize") then
        local owner = baseModel.Owner.Value
        return owner and owner ~= player
    end
    return false
end

-- Função para atacar base (versão simplificada)
local function attackBase(baseModel)
    if not baseModel or not baseModel:FindFirstChild("BaseSize") then return end
    
    local baseSizeValue = baseModel.BaseSize
    baseSizeValue.Value = math.max(0, baseSizeValue.Value - 1)
    
    print("💥 " .. player.Name .. " atacou " .. baseModel.Name .. "! Tamanho: " .. baseSizeValue.Value)
end

-- Função para iniciar coleta/ataque
local function startCollectingResource()
    isCollecting = true
    
    collectConnection = RunService.Heartbeat:Connect(function()
        if not isCollecting then
            if collectConnection then
                collectConnection:Disconnect()
                collectConnection = nil
            end
            return
        end
        
        -- Raycast para detectar alvo
        local character = player.Character
        if not character then return end
        
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if not humanoidRootPart then return end
        
        -- Posição de origem
        local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
        local direction = (mouse.Hit.Position - startPos).Unit
        
        -- Raycast
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {character}
        
        local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
        
        if raycastResult then
            local hitObject = raycastResult.Instance
            local endPos = raycastResult.Position
            
            -- Verifica se é um recurso coletável
            if isCollectable(hitObject) then
                currentTarget = hitObject
                
                -- Cria efeito visual
                createBeamEffect(startPos, endPos, BrickColor.new("Bright blue"))
                
                -- Coleta o recurso
                collectResource(hitObject)
                
            -- Verifica se é uma base inimiga
            elseif isEnemyBase(hitObject) then
                currentTarget = hitObject
                
                -- Cria efeito visual vermelho para ataque
                createBeamEffect(startPos, endPos, BrickColor.new("Bright red"))
                
                -- Ataca a base
                attackBase(hitObject.Parent)
                
            else
                currentTarget = nil
            end
        else
            currentTarget = nil
        end
    end)
end

-- Função para parar coleta/ataque
local function stopCollectingResource()
    isCollecting = false
    
    if collectConnection then
        collectConnection:Disconnect()
        collectConnection = nil
    end
    
    currentTarget = nil
end

-- Monitora quando o jogador equipa a CollectorGun
local function monitorCollectorGun()
    local character = player.Character or player.CharacterAdded:Wait()
    
    character.ChildAdded:Connect(function(child)
        if child.Name == "CollectorGun" and child:IsA("Tool") then
            print("🔨 CollectorGun equipada!")
            mouse.Icon = "rbxasset://textures/ArrowCursor.png"
            
            child.Activated:Connect(startCollectingResource)
            child.Deactivated:Connect(stopCollectingResource)
            
            child.Unequipped:Connect(function()
                stopCollectingResource()
                mouse.Icon = ""
                print("🔨 CollectorGun desequipada!")
            end)
        end
    end)
    
    -- Verifica se já tem a ferramenta equipada
    local existingTool = character:FindFirstChild("CollectorGun")
    if existingTool and existingTool:IsA("Tool") then
        print("🔨 CollectorGun já equipada!")
        mouse.Icon = "rbxasset://textures/ArrowCursor.png"
        
        existingTool.Activated:Connect(startCollectingResource)
        existingTool.Deactivated:Connect(stopCollectingResource)
        
        existingTool.Unequipped:Connect(function()
            stopCollectingResource()
            mouse.Icon = ""
            print("🔨 CollectorGun desequipada!")
        end)
    end
end

-- Inicializa o monitoramento
if player.Character then
    monitorCollectorGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    monitorCollectorGun()
end)

print("✅ SimpleCollectorScript carregado!")